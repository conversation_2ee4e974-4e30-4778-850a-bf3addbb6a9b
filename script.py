import os
import sounddevice as sd
import numpy as np
import tempfile
import subprocess
from pynput.keyboard import Controller, Listener, Key
import threading
import time
import queue
import collections
import re
from dataclasses import dataclass
from typing import List, Optional
from datetime import datetime

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
    DOTENV_AVAILABLE = True
    print("📄 .env file loaded")
except ImportError:
    DOTENV_AVAILABLE = False
    print("⚠️ python-dotenv not available - using system environment variables")

# Try to import MLX-Whisper for Apple Silicon optimization
try:
    import mlx_whisper
    MLX_WHISPER_AVAILABLE = True
    print("🚀 MLX-Whisper loaded - Apple Silicon optimization enabled")
except ImportError:
    MLX_WHISPER_AVAILABLE = False
    print("⚠️ MLX-Whisper not available - using whisper.cpp fallback")

# Try to import OpenAI for edit functionality
try:
    import openai
    OPENAI_AVAILABLE = True
    print("🧠 OpenAI integration available for edit functionality")
except ImportError:
    OPENAI_AVAILABLE = False
    print("⚠️ OpenAI not available - edit functionality disabled")

# Settings
DURATION = 5  # Seconds to listen (for Enter key)
SAMPLERATE = 44100
MODEL_PATH = os.path.expanduser("/Users/<USER>/Desktop/Dictate/models/ggml-large-v3.bin")
WHISPER_CPP_PATH = "/opt/homebrew/bin/whisper-cli"  # or path to whisper binary

# Transcription Engine Configuration
USE_MLX_WHISPER = MLX_WHISPER_AVAILABLE  # Use MLX-Whisper if available, fallback to whisper.cpp
MLX_MODEL = "mlx-community/whisper-large-v3-turbo"  # MLX model to use (corrected path)

# Edit functionality configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
OPENAI_MODEL = os.getenv("OPENAI_MODEL", "gpt-3.5-turbo")
MEMORY_BUFFER_SIZE = int(os.getenv("MEMORY_BUFFER_SIZE", "10"))
EDIT_MAX_TOKENS = int(os.getenv("EDIT_MAX_TOKENS", "500"))
EDIT_TEMPERATURE = float(os.getenv("EDIT_TEMPERATURE", "0.3"))

# Text selection modifier configuration
TEXT_SELECTION_MODIFIER_STR = os.getenv("TEXT_SELECTION_MODIFIER", "cmd").lower()
if TEXT_SELECTION_MODIFIER_STR == "cmd":
    TEXT_SELECTION_MODIFIER = Key.cmd
elif TEXT_SELECTION_MODIFIER_STR == "ctrl":
    TEXT_SELECTION_MODIFIER = Key.ctrl
else:
    print(f"⚠️ Invalid TEXT_SELECTION_MODIFIER: {TEXT_SELECTION_MODIFIER_STR}. Using 'cmd' as default.")
    TEXT_SELECTION_MODIFIER = Key.cmd

# F3 contextual recording configuration (enhances transcription with context)
F3_ENABLED = os.getenv("F3_ENABLED", "true").lower() == "true"
F3_MEMORY_COUNT = int(os.getenv("F3_MEMORY_COUNT", "5"))
F3_SYSTEM_PROMPT = os.getenv("F3_SYSTEM_PROMPT", 
    "You are a writing assistant that enhances dictated text with contextual awareness. "
    "Given previous statements from the current session and new dictation, enhance the new dictation by: "
    "resolving ambiguous references ('that', 'it', 'this', etc.) using context, "
    "adding specificity where context provides clear meaning, "
    "preserving the user's original tone and intent, "
    "and not adding information beyond what context clearly implies. "
    "Return only the enhanced dictation, no explanations.")

# F4 contextual awareness configuration (edits last text with context)
CONTEXTUAL_ENABLED = os.getenv("CONTEXTUAL_ENABLED", "true").lower() == "true"
CONTEXTUAL_MEMORY_COUNT = int(os.getenv("CONTEXTUAL_MEMORY_COUNT", "5"))
CONTEXTUAL_SYSTEM_PROMPT = os.getenv("CONTEXTUAL_SYSTEM_PROMPT", 
    "You are a writing assistant that enhances dictated text with contextual awareness. "
    "Given previous statements from the current session and new dictation, enhance the new dictation by: "
    "resolving ambiguous references ('that', 'it', 'this', etc.) using context, "
    "adding specificity where context provides clear meaning, "
    "preserving the user's original tone and intent, "
    "and not adding information beyond what context clearly implies. "
    "Return only the enhanced dictation, no explanations.")

USE_EDIT_FUNCTIONALITY = OPENAI_AVAILABLE and OPENAI_API_KEY
USE_F3_FUNCTIONALITY = OPENAI_AVAILABLE and OPENAI_API_KEY and F3_ENABLED
USE_CONTEXTUAL_FUNCTIONALITY = OPENAI_AVAILABLE and OPENAI_API_KEY and CONTEXTUAL_ENABLED
EDIT_HOTKEY = Key.f6  # Press F6 to record edit instruction
CONTEXTUAL_HOTKEY = Key.f4  # Press F4 for contextual editing
F3_HOTKEY = Key.f3  # Press F3 for contextual recording

keyboard = Controller()
HOTKEY_KEY = Key.f8  # Press F8 to toggle recording
VAD_HOTKEY = Key.f7  # Press F7 for voice-activated recording

# Global state for toggle recording
is_recording = False
recording_thread = None
audio_queue = queue.Queue()
last_toggle_time = 0

# Global state for VAD recording
vad_recording = False

# Global state for edit functionality
is_edit_recording = False
edit_recording_thread = None
last_edit_toggle_time = 0

# Global state for F3 contextual recording functionality
is_f3_recording = False
f3_recording_thread = None
last_f3_toggle_time = 0

# Global state for F4 contextual editing functionality
is_contextual_recording = False
contextual_recording_thread = None
last_contextual_toggle_time = 0

content_memory = None
llm_client = None

# Voice activity detection settings
SILENCE_THRESHOLD = 500  # Amplitude threshold for silence
SILENCE_DURATION = 3.0  # Seconds of silence before stopping

# --- new globals for continuous stream VAD ---
_stream_q = queue.Queue()
FRAME_MS        = 20            # frame size for VAD / 50 FPS
FRAME_SAMPLES   = int(SAMPLERATE * FRAME_MS / 1000)
MAX_STREAM_SECS = 300           # safety limit: 5 min
VAD_PAD_FRAMES  = int(SILENCE_DURATION * 1000 / FRAME_MS)

@dataclass
class ContentEntry:
    """Represents a piece of typed content with metadata"""
    text: str
    word_count: int
    timestamp: datetime
    char_count: int

class ContentMemory:
    """Manages memory of recently typed content for edit functionality"""
    
    def __init__(self, buffer_size: int = MEMORY_BUFFER_SIZE):
        self.buffer_size = buffer_size
        self.entries: List[ContentEntry] = []
    
    def add_content(self, text: str):
        """Add new content to memory buffer"""
        if not text.strip():
            return
            
        entry = ContentEntry(
            text=text,
            word_count=len(text.split()),
            char_count=len(text),
            timestamp=datetime.now()
        )
        
        self.entries.append(entry)
        
        # Keep only the most recent entries
        if len(self.entries) > self.buffer_size:
            self.entries.pop(0)
    
    def get_last_entry(self) -> Optional[ContentEntry]:
        """Get the most recently typed content"""
        return self.entries[-1] if self.entries else None
    
    def get_entries(self, count: int = None) -> List[ContentEntry]:
        """Get recent entries (most recent first)"""
        if count is None:
            return list(reversed(self.entries))
        return list(reversed(self.entries[-count:]))
    
    def get_contextual_memory(self, count: int = CONTEXTUAL_MEMORY_COUNT, exclude_last: bool = False) -> str:
        """Get formatted context for LLM enhancement"""
        if not self.entries:
            return ""
        
        # Get entries for context, optionally excluding the very last one
        if exclude_last and len(self.entries) > 1:
            # Exclude the last entry and get the previous 'count' entries
            available_entries = self.entries[:-1]
            recent_entries = available_entries[-count:] if len(available_entries) >= count else available_entries[:]
        else:
            # Get the most recent entries
            recent_entries = self.entries[-count:] if len(self.entries) >= count else self.entries[:]
        
        if not recent_entries:
            return ""
        
        # Format as chronological bullet points
        context_lines = [f"- \"{entry.text}\"" for entry in recent_entries]
        return "Previous statements (chronological order):\n" + "\n".join(context_lines)

class LLMClient:
    """Handles LLM API calls for edit functionality"""
    
    def __init__(self, api_key: str):
        self.client = openai.OpenAI(api_key=api_key) if OPENAI_AVAILABLE else None
    
    def edit_text(self, original_text: str, instruction: str, context: str = "", use_contextual: bool = False) -> Optional[str]:
        """Process edit instruction using LLM, optionally with contextual awareness"""
        if not self.client:
            print("❌ LLM client not available")
            return None
        
        try:
            if use_contextual:
                # Use contextual system prompt with context
                if context:
                    prompt = f"""{CONTEXTUAL_SYSTEM_PROMPT}

{context}

Apply the above behavior to this text: "{original_text}"
Based on this instruction: "{instruction}"

Return only the modified text:"""
                else:
                    prompt = f"""{CONTEXTUAL_SYSTEM_PROMPT}

Apply the above behavior to this text: "{original_text}"
Based on this instruction: "{instruction}"

Return only the modified text:"""
            else:
                # Use standard edit prompt
                prompt = f"""Edit this text according to the instruction. Return only the edited text, no explanations.

Original text: "{original_text}"
Instruction: "{instruction}"

Edited text:"""
            
            response = self.client.chat.completions.create(
                model=OPENAI_MODEL,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=EDIT_MAX_TOKENS,
                temperature=EDIT_TEMPERATURE
            )
            
            edited_text = response.choices[0].message.content.strip()
            # Remove quotes if the response is wrapped in them
            if edited_text.startswith('"') and edited_text.endswith('"'):
                edited_text = edited_text[1:-1]
            
            return edited_text
            
        except Exception as e:
            print(f"❌ LLM edit error: {e}")
            return None
    
    def enhance_transcription(self, transcription: str, context: str = "") -> Optional[str]:
        """Enhance transcription with contextual awareness using F3 system prompt"""
        if not self.client:
            print("❌ LLM client not available")
            return None
        
        try:
            if context:
                prompt = f"""{F3_SYSTEM_PROMPT}

Previous context from this session:
{context}

Enhance this new dictation: "{transcription}" """
            else:
                # No context available, but still use system prompt for enhancement
                prompt = f"""{F3_SYSTEM_PROMPT}

Enhance this dictation: "{transcription}" """
            
            response = self.client.chat.completions.create(
                model=OPENAI_MODEL,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=EDIT_MAX_TOKENS,
                temperature=EDIT_TEMPERATURE
            )
            
            enhanced_text = response.choices[0].message.content.strip()
            # Remove quotes if the response is wrapped in them
            if enhanced_text.startswith('"') and enhanced_text.endswith('"'):
                enhanced_text = enhanced_text[1:-1]
            
            return enhanced_text
            
        except Exception as e:
            print(f"❌ LLM transcription enhancement error: {e}")
            return None

def select_and_replace_text(original_text: str, new_text: str):
    """Select previous text and replace it with new text"""
    if not original_text.strip():
        return
        
    try:
        # Calculate how many words to select backwards
        word_count = len(original_text.split())
        
        # Select the previous text by moving word by word backwards
        for _ in range(word_count):
            keyboard.press(TEXT_SELECTION_MODIFIER)  # Use configurable modifier (cmd/ctrl)
            keyboard.press(Key.shift)
            keyboard.press(Key.left)
            keyboard.release(Key.left)
            keyboard.release(Key.shift)
            keyboard.release(TEXT_SELECTION_MODIFIER)
            time.sleep(0.01)
        
        # Delete the selected text
        keyboard.press(Key.backspace)
        keyboard.release(Key.backspace)
        time.sleep(0.05)
        
        # Type the new text
        type_text_without_memory(new_text)
        
        print(f"✏️ Replaced: '{original_text}' → '{new_text}'")
        
    except Exception as e:
        print(f"❌ Text replacement error: {e}")

def record_audio(duration=5, fs=16000):
    print("🎙️ Listening...")
    audio = sd.rec(int(duration * fs), samplerate=fs, channels=1, dtype='int16')
    sd.wait()
    return audio

def record_audio_continuous(fs=16000):
    """Record audio continuously until stopped"""
    global is_recording
    print("🎙️ Recording started (press F8 to stop)...")
    
    # Start recording with a large buffer
    max_duration = 300  # 5 minutes max
    audio = sd.rec(int(max_duration * fs), samplerate=fs, channels=1, dtype='int16')
    
    # Wait until recording is stopped
    start_time = time.time()
    while is_recording:
        time.sleep(0.1)
    
    # Stop the recording
    sd.stop()
    duration = time.time() - start_time
    samples_recorded = int(duration * fs)
    
    # Return only the recorded portion
    if samples_recorded > 0:
        audio = audio[:samples_recorded]
        print(f"🎙️ Recording stopped. Total duration: {duration:.1f} seconds")
        return audio
    return None

def record_audio_with_vad(fs=SAMPLERATE):
    """Continuous microphone stream with simple RMS VAD."""
    global vad_recording

    print("🎙️ Recording... speak naturally.")
    frames = collections.deque(maxlen=VAD_PAD_FRAMES)   # rolling window for silence check
    captured = []                                       # all voice frames
    silent_counter = 0

    with sd.InputStream(samplerate=fs,
                        channels=1,
                        dtype='int16',
                        blocksize=FRAME_SAMPLES,
                        callback=_audio_cb):
        start = time.time()
        while vad_recording:
            try:
                frame = _stream_q.get(timeout=0.1)
            except queue.Empty:
                continue

            frames.append(frame)
            amp = np.abs(frame).mean()

            # Simple amplitude VAD — swap for webrtcvad for pro-grade
            if amp < SILENCE_THRESHOLD:
                silent_counter += 1
                if silent_counter >= VAD_PAD_FRAMES:
                    print("🔇   silence > %.1fs → stop" % SILENCE_DURATION)
                    break
            else:
                silent_counter = 0
                captured.append(frame)

            # safety shut-off
            if time.time() - start > MAX_STREAM_SECS:
                print("⏱️  max duration hit")
                break

    vad_recording = False
    if not captured:
        return None
    return np.concatenate(captured)

def _audio_cb(indata, frames, time_info, status):
    """Push each frame into a thread-safe queue."""
    if status:
        print("⚠️ stream status:", status)
    _stream_q.put(indata.copy())

def save_wav(data, filename, fs):
    import wave
    # ensure the data is in 16‑bit PCM format expected by whisper‑cli
    data = data.astype(np.int16)
    with wave.open(filename, 'wb') as wf:
        wf.setnchannels(1)
        wf.setsampwidth(2)
        wf.setframerate(fs)
        wf.writeframes(data.tobytes())

def transcribe_wav_mlx(wav_path):
    """Transcribe using MLX-Whisper for Apple Silicon optimization"""
    print("🚀 Transcribing with MLX-Whisper (Apple Silicon optimized)...")
    try:
        result = mlx_whisper.transcribe(
            wav_path,
            path_or_hf_repo=MLX_MODEL,
            verbose=False
        )
        return result["text"].strip()
    except Exception as e:
        print(f"❌ MLX-Whisper error: {e}")
        return ""

def transcribe_wav_cpp(wav_path):
    """Transcribe using whisper.cpp (fallback method)"""
    print("🧠 Transcribing with whisper.cpp...")
    result = subprocess.run([
        WHISPER_CPP_PATH,
        "-m", MODEL_PATH,
        "-f", wav_path,
        "--language", "en",
        "--output-txt",
        "--output-file", wav_path,      # saves <wav>.txt next to the wav
        "--no-speech-thold", "0.2"      # more permissive speech detection
    ], capture_output=True)
    print("🪵 whisper stdout:", result.stdout.decode())
    print("🪵 whisper stderr:", result.stderr.decode())

    txt_path = wav_path + ".txt"
    if os.path.exists(txt_path):
        with open(txt_path, 'r') as f:
            return f.read().strip()
    return ""

def transcribe_wav(wav_path):
    """Main transcription function - uses MLX-Whisper if available, otherwise whisper.cpp"""
    if USE_MLX_WHISPER:
        return transcribe_wav_mlx(wav_path)
    else:
        return transcribe_wav_cpp(wav_path)

def process_commands(text):
    """Process voice commands in the transcribed text"""
    # Define regular command mappings
    commands = {
        "new line": "\n",
        "line break": "\n",
        "new paragraph": "\n\n",
        "period": ".",
        "comma": ",",
        "question mark": "?",
        "exclamation mark": "!",
        "exclamation point": "!",
        "colon": ":",
        "semicolon": ";",
        "open quote": '"',
        "close quote": '"',
        "open paren": "(",
        "close paren": ")",
    }
    
    # Process each command with regex to handle punctuation and capitalization
    processed = text
    for command, replacement in commands.items():
        # Create a regex pattern that matches the command with optional punctuation
        # This pattern captures and removes trailing punctuation
        pattern = r'\b' + re.escape(command) + r'[.,!?;:]*\s*'
        processed = re.sub(pattern, replacement, processed, flags=re.IGNORECASE)
    
    # Clean up any double spaces that might result
    processed = re.sub(r'  +', ' ', processed)
    
    return processed

def handle_edit_instruction(instruction: str, use_contextual: bool = False):
    """Handle edit instruction by processing with LLM, optionally with contextual awareness"""
    global content_memory, llm_client
    
    if not content_memory or not llm_client:
        print("❌ Edit system not initialized")
        return
    
    last_entry = content_memory.get_last_entry()
    if not last_entry:
        print("❌ No recent text to edit")
        return
    
    if use_contextual:
        # Get context from memory (excluding the last entry we're modifying)
        context = content_memory.get_contextual_memory(CONTEXTUAL_MEMORY_COUNT, exclude_last=True)
        print(f"🧠 Processing contextual edit: '{instruction}' on '{last_entry.text}'")
        edited_text = llm_client.edit_text(last_entry.text, instruction, context, use_contextual=True)
    else:
        print(f"🧠 Processing edit: '{instruction}' on '{last_entry.text}'")
        edited_text = llm_client.edit_text(last_entry.text, instruction)
    
    if edited_text:
        # Replace the previous text with the edited version
        select_and_replace_text(last_entry.text, edited_text)
        # Update memory with the new text
        content_memory.add_content(edited_text)
    else:
        print("❌ Failed to process edit instruction")

def type_text_without_memory(text):
    """Type text without adding to memory (used for replacements)"""
    print(f"⌨️ Typing: {text}")
    for char in text:
        keyboard.type(char)
        time.sleep(0.01)  # mimic human typing speed

def type_text(text):
    """Type text and add to memory for edit functionality"""
    global content_memory
    
    # Process commands first
    processed_text = process_commands(text)
    
    # If process_commands returned empty string (e.g., edit command), don't type or store
    if not processed_text:
        return
    
    print(f"⌨️ Typing: {processed_text}")
    for char in processed_text:
        keyboard.type(char)
        time.sleep(0.01)  # mimic human typing speed
    
    # Add to memory for edit functionality
    if content_memory and processed_text.strip():
        content_memory.add_content(processed_text)

def handle_record():
    audio = record_audio(DURATION, SAMPLERATE)
    with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tf:
        save_wav(audio, tf.name, SAMPLERATE)
        text = transcribe_wav(tf.name)
        if text:
            type_text(text)
        else:
            print("😕 No speech detected.")
    os.unlink(tf.name)

def handle_toggle_record():
    global is_recording, recording_thread, audio_queue, last_toggle_time
    
    # Debounce - ignore if less than 0.5 seconds since last toggle
    current_time = time.time()
    if current_time - last_toggle_time < 0.5:
        return
    last_toggle_time = current_time
    
    if not is_recording:
        # Start recording
        is_recording = True
        audio_queue = queue.Queue()  # Create new queue
        recording_thread = threading.Thread(target=lambda: process_toggle_recording(), daemon=True)
        recording_thread.start()
    else:
        # Stop recording
        is_recording = False
        
def process_toggle_recording():
    audio = record_audio_continuous(SAMPLERATE)
    if audio is not None:
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tf:
            save_wav(audio, tf.name, SAMPLERATE)
            text = transcribe_wav(tf.name)
            if text:
                type_text(text)
            else:
                print("😕 No speech detected.")
        os.unlink(tf.name)

def handle_vad_record():
    """Handle voice-activated recording"""
    global vad_recording
    
    if vad_recording:
        print("🔄 VAD recording already in progress...")
        return
    
    vad_recording = True
    threading.Thread(target=process_vad_recording, daemon=True).start()

def process_vad_recording():
    audio = record_audio_with_vad(SAMPLERATE)
    if audio is not None:
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tf:
            save_wav(audio, tf.name, SAMPLERATE)
            text = transcribe_wav(tf.name)
            if text:
                type_text(text)
            else:
                print("😕 No speech detected.")
        os.unlink(tf.name)

def handle_edit_record():
    """Handle F6 key press - toggle edit instruction recording"""
    global is_edit_recording, edit_recording_thread, last_edit_toggle_time
    
    # Debounce - ignore if less than 0.5 seconds since last toggle
    current_time = time.time()
    if current_time - last_edit_toggle_time < 0.5:
        return
    last_edit_toggle_time = current_time
    
    if not USE_EDIT_FUNCTIONALITY:
        print("❌ Edit functionality not available. Set OPENAI_API_KEY in .env file.")
        return
    
    if not content_memory or not content_memory.get_last_entry():
        print("❌ No recent text to edit")
        return
    
    if not is_edit_recording:
        # Start edit recording
        is_edit_recording = True
        edit_recording_thread = threading.Thread(target=process_edit_recording, daemon=True)
        edit_recording_thread.start()
    else:
        # Stop edit recording
        is_edit_recording = False

def record_edit_instruction_continuous(fs=SAMPLERATE):
    """Record edit instruction continuously until stopped"""
    global is_edit_recording
    print("🎤 Recording edit instruction (press F6 to stop)...")
    
    # Start recording with a large buffer
    max_duration = 60  # 1 minute max for edit instructions
    audio = sd.rec(int(max_duration * fs), samplerate=fs, channels=1, dtype='int16')
    
    # Wait until recording is stopped
    start_time = time.time()
    while is_edit_recording:
        time.sleep(0.1)
    
    # Stop the recording
    sd.stop()
    duration = time.time() - start_time
    samples_recorded = int(duration * fs)
    
    # Return only the recorded portion
    if samples_recorded > 0:
        audio = audio[:samples_recorded]
        print(f"🎤 Edit recording stopped. Duration: {duration:.1f} seconds")
        return audio
    return None

def process_edit_recording():
    """Process recorded edit instruction"""
    audio = record_edit_instruction_continuous(SAMPLERATE)
    if audio is not None:
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tf:
            save_wav(audio, tf.name, SAMPLERATE)
            instruction = transcribe_wav(tf.name)
            if instruction:
                handle_edit_instruction(instruction)
            else:
                print("😕 No edit instruction detected.")
        os.unlink(tf.name)

def handle_contextual_record():
    """Handle F4 key press - toggle contextual recording"""
    global is_contextual_recording, contextual_recording_thread, last_contextual_toggle_time
    
    # Debounce - ignore if less than 0.5 seconds since last toggle
    current_time = time.time()
    if current_time - last_contextual_toggle_time < 0.5:
        return
    last_contextual_toggle_time = current_time
    
    if not USE_CONTEXTUAL_FUNCTIONALITY:
        print("❌ Contextual functionality not available. Set OPENAI_API_KEY and CONTEXTUAL_ENABLED=true in .env file.")
        return
    
    if not is_contextual_recording:
        # Start contextual recording
        is_contextual_recording = True
        contextual_recording_thread = threading.Thread(target=process_contextual_recording, daemon=True)
        contextual_recording_thread.start()
    else:
        # Stop contextual recording
        is_contextual_recording = False

def record_contextual_instruction_continuous(fs=SAMPLERATE):
    """Record contextual instruction continuously until stopped"""
    global is_contextual_recording
    print("🎤 Recording with contextual awareness (press F4 to stop)...")
    
    # Start recording with a large buffer
    max_duration = 60  # 1 minute max for contextual recordings
    audio = sd.rec(int(max_duration * fs), samplerate=fs, channels=1, dtype='int16')
    
    # Wait until recording is stopped
    start_time = time.time()
    while is_contextual_recording:
        time.sleep(0.1)
    
    # Stop the recording
    sd.stop()
    duration = time.time() - start_time
    samples_recorded = int(duration * fs)
    
    # Return only the recorded portion
    if samples_recorded > 0:
        audio = audio[:samples_recorded]
        print(f"🎤 Contextual recording stopped. Duration: {duration:.1f} seconds")
        return audio
    return None

def process_contextual_recording():
    """Process recorded contextual instruction"""
    audio = record_contextual_instruction_continuous(SAMPLERATE)
    if audio is not None:
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tf:
            save_wav(audio, tf.name, SAMPLERATE)
            instruction = transcribe_wav(tf.name)
            if instruction:
                # Use the existing edit infrastructure but with contextual awareness
                handle_edit_instruction(instruction, use_contextual=True)
            else:
                print("😕 No instruction detected.")
        os.unlink(tf.name)

def handle_f3_record():
    """Handle F3 key press - toggle contextual recording (like F8 but context-enhanced)"""
    global is_f3_recording, f3_recording_thread, last_f3_toggle_time
    
    # Debounce - ignore if less than 0.5 seconds since last toggle
    current_time = time.time()
    if current_time - last_f3_toggle_time < 0.5:
        return
    last_f3_toggle_time = current_time
    
    if not USE_F3_FUNCTIONALITY:
        print("❌ F3 contextual recording not available. Set OPENAI_API_KEY and F3_ENABLED=true in .env file.")
        return
    
    if not is_f3_recording:
        # Start F3 contextual recording
        is_f3_recording = True
        f3_recording_thread = threading.Thread(target=process_f3_recording, daemon=True)
        f3_recording_thread.start()
    else:
        # Stop F3 contextual recording
        is_f3_recording = False

def record_f3_continuous(fs=SAMPLERATE):
    """Record F3 audio continuously until stopped (like F8)"""
    global is_f3_recording
    print("🎤 Recording with context enhancement (press F3 to stop)...")
    
    # Start recording with a large buffer
    max_duration = 300  # 5 minutes max
    audio = sd.rec(int(max_duration * fs), samplerate=fs, channels=1, dtype='int16')
    
    # Wait until recording is stopped
    start_time = time.time()
    while is_f3_recording:
        time.sleep(0.1)
    
    # Stop the recording
    sd.stop()
    duration = time.time() - start_time
    samples_recorded = int(duration * fs)
    
    # Return only the recorded portion
    if samples_recorded > 0:
        audio = audio[:samples_recorded]
        print(f"🎤 F3 recording stopped. Duration: {duration:.1f} seconds")
        return audio
    return None

def process_f3_recording():
    """Process F3 contextual recording (enhance transcription with context)"""
    global content_memory, llm_client
    
    audio = record_f3_continuous(SAMPLERATE)
    if audio is not None:
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tf:
            save_wav(audio, tf.name, SAMPLERATE)
            raw_transcription = transcribe_wav(tf.name)
            if raw_transcription:
                # Get context from memory for enhancement
                context = content_memory.get_contextual_memory(F3_MEMORY_COUNT) if content_memory else ""
                
                # Enhance transcription with contextual awareness
                print(f"🧠 Enhancing transcription: '{raw_transcription}'")
                if llm_client:
                    enhanced_text = llm_client.enhance_transcription(raw_transcription, context)
                    if enhanced_text:
                        print(f"✨ Enhanced: '{enhanced_text}'")
                        type_text(enhanced_text)
                    else:
                        print("❌ Failed to enhance, using original")
                        type_text(raw_transcription)
                else:
                    # No LLM client available, just type the original
                    print("⚠️ LLM not available, typing original")
                    type_text(raw_transcription)
            else:
                print("😕 No speech detected.")
        os.unlink(tf.name)

def start_hotkey_listener():
    def on_press(key):
        if key == HOTKEY_KEY:
            handle_toggle_record()
        elif key == VAD_HOTKEY:
            handle_vad_record()
        elif key == EDIT_HOTKEY:
            handle_edit_record()
        elif key == CONTEXTUAL_HOTKEY:
            handle_contextual_record()
        elif key == F3_HOTKEY:
            handle_f3_record()
    listener = Listener(on_press=on_press)
    listener.daemon = True
    listener.start()

def initialize_edit_system():
    """Initialize the edit functionality system"""
    global content_memory, llm_client
    
    content_memory = ContentMemory()
    
    if USE_EDIT_FUNCTIONALITY:
        llm_client = LLMClient(OPENAI_API_KEY)
        print("✨ Edit functionality initialized - press F6 to edit recent text")
        if USE_F3_FUNCTIONALITY:
            print("✨ F3 contextual recording initialized - press F3 for context-enhanced transcription")
        if USE_CONTEXTUAL_FUNCTIONALITY:
            print("✨ F4 contextual editing initialized - press F4 for context-enhanced editing")
    else:
        print("⚠️ Edit functionality disabled - set OPENAI_API_KEY to enable")
        if F3_ENABLED:
            print("⚠️ F3 contextual recording disabled - requires OPENAI_API_KEY")
        if CONTEXTUAL_ENABLED:
            print("⚠️ F4 contextual editing disabled - requires OPENAI_API_KEY")

def main():
    # Initialize edit system
    initialize_edit_system()
    
    # start the hot‑key listener in the background
    start_hotkey_listener()
    print("🎤 Ready. Commands:")
    print("  • F7: Auto-stop recording (stops after 2 seconds of silence)")
    print("  • F8: Toggle recording (press to start/stop)")
    print("  • Enter: 5-second recording")
    if USE_F3_FUNCTIONALITY:
        print("  • F3: Toggle contextual recording (context-enhanced transcription)")
    if USE_EDIT_FUNCTIONALITY:
        print("  • F6: Toggle edit instruction recording (press to start/stop)")
    if USE_CONTEXTUAL_FUNCTIONALITY:
        print("  • F4: Toggle contextual editing (context-enhanced editing)")
    print("  • Ctrl+C: Quit")
    while True:
        try:
            input()  # wait for Enter
            handle_record()
        except KeyboardInterrupt:
            print("\n👋 Exiting.")
            break

if __name__ == "__main__":
    main()