# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Dictate is a dual-implementation voice-to-text transcription app with both Python and Swift versions:

1. **Python Implementation** (`script.py`): Full-featured command-line tool with MLX-Whisper optimization for Apple Silicon
2. **Swift Implementation** (`DictateApp/`): Native macOS app using SwiftUI with repository pattern architecture

## Commands

### Python Version
```bash
# Install dependencies
pip install -r requirements.txt

# Run the application
python script.py

# Environment configuration via .env file (see README.md for all options)
```

### Swift Version
```bash
# Build the Swift application
cd DictateApp
swift build

# Run the Swift application
swift run

# Build for release
swift build -c release
```

## Architecture

### Swift App Architecture (Repository Pattern)
The Swift app follows a clean architecture with clear separation of concerns:

- **Models/**: Data structures (`TranscriptionModels.swift`, `AppSettings.swift`, `PermissionsStatus.swift`)
- **Repositories/**: Data access layer with protocol-based abstractions
  - `TranscriptionRepository` - MLX Whisper integration
  - `AudioCaptureRepository` - AVFoundation audio capture
  - `LLMRepository` - OpenAI API integration
  - `TextOutputRepository` - System text input/clipboard
  - `PermissionsRepository` - System permissions management
- **Services/**: Business logic (`AppState.swift`, `VADManager.swift`)
- **ViewModels/**: UI business logic (`MainViewModel.swift`)
- **Views/**: SwiftUI user interface

### Python Implementation Architecture
- Modular design with MLX-Whisper/whisper.cpp fallback
- Keyboard shortcuts for different recording modes (F7, F8, F3, F4, F6)
- OpenAI integration for contextual transcription and editing
- Voice command processing for punctuation

## Key Features

### Recording Modes
- **F7**: Voice Activity Detection (auto-stop after silence)
- **F8**: Toggle recording (press to start/stop)
- **F3**: Contextual recording (AI-enhanced transcription)
- **F6**: Edit instruction recording
- **F4**: Contextual editing

### Apple Silicon Optimization
Both implementations leverage MLX framework for Apple Neural Engine acceleration, with graceful fallbacks to CPU-based processing.

## Dependencies

### Swift Dependencies (Package.swift)
- MLX Swift (mlx-swift) - Apple Silicon ML acceleration
- SwiftOpenAI - OpenAI API integration
- Requires macOS 14+

### Python Dependencies (requirements.txt)
- MLX-Whisper for Apple Silicon optimization
- OpenAI for LLM features
- SoundDevice/PyNput for audio/keyboard handling

## Models Directory
Pre-trained Whisper models stored in `models/` directory:
- Multiple quantization levels (q5_0, q8_0, full precision)
- Various model sizes (medium, large, large-v3, large-v3-turbo)

## Configuration
- Swift: Settings managed via `AppSettings.swift` with UserDefaults persistence  
- Python: Environment variables via `.env` file (see README.md for full configuration options)