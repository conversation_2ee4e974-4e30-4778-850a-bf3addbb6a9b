#!/usr/bin/env swift

import Foundation
import ApplicationServices

print("🧪 Testing Accessibility Permissions")

// Check accessibility permission
let trusted = AXIsProcessTrusted()
print("Accessibility permission granted: \(trusted)")

if !trusted {
    print("❌ App needs Accessibility permissions for hotkeys to work")
    print("📋 Go to System Settings > Privacy & Security > Accessibility")
    print("📋 Add this app to the list and enable it")
} else {
    print("✅ Accessibility permissions are granted - hotkeys should work")
}

print("\n✅ Permission test complete")
