#!/usr/bin/env swift

import Foundation
import ApplicationServices

print("🧪 Testing Bundle Information and Permissions")

// Check bundle identifier
if let bundleId = Bundle.main.bundleIdentifier {
    print("Bundle Identifier: \(bundleId)")
} else {
    print("❌ No bundle identifier found")
}

// Check executable name
if let executableName = Bundle.main.executableURL?.lastPathComponent {
    print("Executable Name: \(executableName)")
} else {
    print("❌ No executable name found")
}

// Check bundle path
print("Bundle Path: \(Bundle.main.bundlePath)")

// Check if running from Xcode vs standalone
let isRunningFromXcode = Bundle.main.bundlePath.contains("DerivedData") || Bundle.main.bundlePath.contains("Build")
print("Running from Xcode: \(isRunningFromXcode)")

// Check accessibility permission
let trusted = AXIsProcessTrusted()
print("Accessibility permission granted: \(trusted)")

// Try to request permission explicitly
print("\nTrying to request accessibility permission...")
let options = [kAXTrustedCheckOptionPrompt.takeUnretainedValue() as String: true]
let trustedWithPrompt = AXIsProcessTrustedWithOptions(options as CFDictionary)
print("Accessibility permission after prompt: \(trustedWithPrompt)")

print("\n✅ Bundle info test complete")
print("\nIf permissions are still false, check System Settings > Privacy & Security > Accessibility")
print("Look for entries matching the bundle identifier or executable name above")
