import Foundation

struct TranscriptionResult: Identifiable, Codable {
    let id = UUID()
    let text: String
    let wordCount: Int
    let charCount: Int
    let timestamp: Date
    let processingTime: TimeInterval?
    
    init(text: String, processingTime: TimeInterval? = nil) {
        self.text = text
        self.wordCount = text.split(separator: " ").count
        self.charCount = text.count
        self.timestamp = Date()
        self.processingTime = processingTime
    }
}

enum RecordingMode: String, CaseIterable {
    case voiceActivity = "vad"
    case toggle = "toggle"
    case contextual = "contextual"
    case edit = "edit"
    case contextualEdit = "contextualEdit"

    var displayName: String {
        switch self {
        case .voiceActivity: return "Voice Activity Detection"
        case .toggle: return "Toggle Recording"
        case .contextual: return "Contextual Recording"
        case .edit: return "Edit Previous Text"
        case .contextualEdit: return "Contextual Edit"
        }
    }

    var description: String {
        switch self {
        case .voiceActivity: return "Auto-stops after silence"
        case .toggle: return "Manual start/stop"
        case .contextual: return "AI-enhanced transcription"
        case .edit: return "Voice edit instructions"
        case .contextualEdit: return "Context-aware editing"
        }
    }
}

enum SystemStatus: Equatable {
    case idle
    case listening(mode: RecordingMode)
    case processing(step: String)
    case error(message: String)
    
    var displayText: String {
        switch self {
        case .idle: return "Ready"
        case .listening(let mode): return "Listening (\(mode.displayName))"
        case .processing(let step): return "Processing: \(step)"
        case .error(let message): return "Error: \(message)"
        }
    }
    
    var isRecording: Bool {
        if case .listening = self { return true }
        return false
    }
}