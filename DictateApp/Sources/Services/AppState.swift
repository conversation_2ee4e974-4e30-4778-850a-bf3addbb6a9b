import SwiftUI
import Combine

@MainActor
class AppState: ObservableObject {
    @Published var systemStatus: SystemStatus = .idle
    @Published var permissions: PermissionsStatus = PermissionsStatus()
    @Published var transcriptionHistory: [TranscriptionResult] = []
    @Published var settings: AppSettings = AppSettings.default
    @Published var isSettingsPresented: Bool = false
    @Published var contentMemory: ContentMemory = ContentMemory()
    
    private let maxHistoryCount = 50
    
    func addTranscription(_ result: TranscriptionResult) {
        transcriptionHistory.insert(result, at: 0)
        // Keep only the most recent transcriptions
        if transcriptionHistory.count > maxHistoryCount {
            transcriptionHistory.removeLast()
        }
    }
    
    func clearHistory() {
        transcriptionHistory.removeAll()
    }
    
    func updateSettings(_ newSettings: AppSettings) {
        print("📱 AppState.updateSettings() called")
        print("📱 New API key: \(newSettings.openAIAPIKey.isEmpty ? "EMPTY" : "SET (\(newSettings.openAIAPIKey.prefix(10))...)")")
        settings = newSettings
        // Save to UserDefaults
        if let data = try? JSONEncoder().encode(settings) {
            UserDefaults.standard.set(data, forKey: "AppSettings")
            print("📱 Settings saved to UserDefaults successfully")
        } else {
            print("❌ Failed to encode settings for saving")
        }
    }
    
    func loadSettings() {
        print("📱 AppState.loadSettings() called")
        if let data = UserDefaults.standard.data(forKey: "AppSettings"),
           let savedSettings = try? JSONDecoder().decode(AppSettings.self, from: data) {
            print("📱 Loaded settings from UserDefaults - API key: \(savedSettings.openAIAPIKey.isEmpty ? "EMPTY" : "SET (\(savedSettings.openAIAPIKey.prefix(10))...)")")
            settings = savedSettings
        } else {
            print("📱 No saved settings found, using defaults")
        }
    }
    
    func updatePermissions(_ newPermissions: PermissionsStatus) {
        permissions = newPermissions
    }
    
    func setError(_ message: String) {
        systemStatus = .error(message: message)
    }
    
    func setIdle() {
        systemStatus = .idle
    }
    
    func setListening(mode: RecordingMode) {
        systemStatus = .listening(mode: mode)
    }
    
    func setProcessing(step: String) {
        systemStatus = .processing(step: step)
    }
    
    func addToContentMemory(_ text: String) {
        contentMemory.addContent(text)
    }
}