import SwiftUI

struct SettingsView: View {
    @EnvironmentObject private var appState: AppState
    @Environment(\.dismiss) private var dismiss
    @State var settings = AppSettings.default
    @State private var showAPIKeyAlert = false
    
    var body: some View {
        let _ = print("🔧 SettingsView.body computed!")
        NavigationView {
            List {
                OpenAISection(settings: $settings, showAPIKeyAlert: $showAPIKeyAlert)
                
                AudioSection(settings: $settings)
                
                TranscriptionSection(settings: $settings)
                
                TextOutputSection(settings: $settings)
                
                AdvancedSection(settings: $settings)
            }
            .navigationTitle("Settings")
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .confirmationAction) {
                    Button("Save") {
                        print("🔧 Save button clicked!")
                        saveSettings()
                    }
                    .buttonStyle(.borderedProminent)
                }
            }
        }
        .frame(width: 600, height: 500)
        .onAppear {
            print("🔧 SettingsView.onAppear() called")
            print("🔧 Current appState.settings API key: \(appState.settings.openAIAPIKey.isEmpty ? "EMPTY" : "SET (\(appState.settings.openAIAPIKey.prefix(10))...)")")
            settings = appState.settings
            print("🔧 Local settings API key: \(settings.openAIAPIKey.isEmpty ? "EMPTY" : "SET (\(settings.openAIAPIKey.prefix(10))...)")")
        }
        .alert("API Key Required", isPresented: $showAPIKeyAlert) {
            Button("OK") { }
        } message: {
            Text("Please enter your OpenAI API key to enable AI features like contextual transcription and text editing.")
        }
    }
    
    private func saveSettings() {
        print("🔧 SettingsView.saveSettings() called")
        print("🔧 API key to save: \(settings.openAIAPIKey.isEmpty ? "EMPTY" : "SET (\(settings.openAIAPIKey.prefix(10))...)")")
        appState.updateSettings(settings)
        print("🔧 appState.updateSettings() completed")
        dismiss()
        print("🔧 Settings dialog dismissed")
    }
}

struct OpenAISection: View {
    @Binding var settings: AppSettings
    @Binding var showAPIKeyAlert: Bool
    @State private var showAPIKey = false
    
    var body: some View {
        Section("OpenAI Configuration") {
            VStack(alignment: .leading, spacing: 8) {
                Text("API Key")
                    .font(.headline)
                
                HStack {
                    if showAPIKey {
                        TextField("sk-...", text: $settings.openAIAPIKey)
                            .textFieldStyle(.roundedBorder)
                    } else {
                        SecureField("sk-...", text: $settings.openAIAPIKey)
                            .textFieldStyle(.roundedBorder)
                    }
                    
                    Button(action: { showAPIKey.toggle() }) {
                        Image(systemName: showAPIKey ? "eye.slash" : "eye")
                    }
                    .buttonStyle(.plain)
                    .help(showAPIKey ? "Hide API key" : "Show API key")
                }
                
                Text("Required for AI features like contextual transcription and text editing")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                if settings.openAIAPIKey.isEmpty {
                    Button("Get API Key") {
                        NSWorkspace.shared.open(URL(string: "https://platform.openai.com/api-keys")!)
                    }
                    .buttonStyle(.link)
                }
            }
            .padding(.vertical, 4)
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Model")
                    .font(.headline)
                
                Picker("OpenAI Model", selection: $settings.openAIModel) {
                    Text("GPT-4o (Recommended)").tag("gpt-4o")
                    Text("GPT-4o mini").tag("gpt-4o-mini")
                    Text("GPT-4 Turbo").tag("gpt-4-turbo")
                    Text("GPT-3.5 Turbo").tag("gpt-3.5-turbo")
                }
                .pickerStyle(.menu)
                
                Text("Higher-tier models provide better accuracy but cost more")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.vertical, 4)
        }
    }
}

struct AudioSection: View {
    @Binding var settings: AppSettings
    
    var body: some View {
        Section("Audio Processing") {
            VStack(alignment: .leading, spacing: 12) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Silence Threshold")
                        .font(.headline)
                    
                    HStack {
                        Slider(value: $settings.silenceThreshold, in: 100...1000, step: 50) {
                            Text("Threshold")
                        }
                        .frame(maxWidth: 200)
                        
                        Text("\(Int(settings.silenceThreshold))")
                            .frame(width: 40, alignment: .trailing)
                            .font(.system(.body, design: .monospaced))
                    }
                    
                    Text("Higher values require louder speech to detect voice activity")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("Silence Duration")
                        .font(.headline)
                    
                    HStack {
                        Slider(value: $settings.silenceDuration, in: 1.0...10.0, step: 0.5) {
                            Text("Duration")
                        }
                        .frame(maxWidth: 200)
                        
                        Text("\(settings.silenceDuration, specifier: "%.1f")s")
                            .frame(width: 40, alignment: .trailing)
                            .font(.system(.body, design: .monospaced))
                    }
                    
                    Text("How long to wait after silence before stopping VAD recording")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.vertical, 4)
        }
    }
}

struct TranscriptionSection: View {
    @Binding var settings: AppSettings
    
    var body: some View {
        Section("Transcription") {
            VStack(alignment: .leading, spacing: 8) {
                Text("Whisper Model")
                    .font(.headline)
                
                Picker("Model", selection: $settings.whisperModel) {
                    ForEach(WhisperModel.allCases, id: \.self) { model in
                        Text(model.displayName).tag(model)
                    }
                }
                .pickerStyle(.menu)
                
                Text("Large models are more accurate but slower")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.vertical, 4)
        }
    }
}

struct TextOutputSection: View {
    @Binding var settings: AppSettings
    @StateObject private var permissionsRepo = SystemPermissionsRepository()
    
    var body: some View {
        Section("Text Output") {
            VStack(alignment: .leading, spacing: 8) {
                Text("Insertion Method")
                    .font(.headline)
                
                Picker("Method", selection: $settings.insertionMethod) {
                    ForEach(InsertionMethod.allCases, id: \.self) { method in
                        Text(method.displayName).tag(method)
                    }
                }
                .pickerStyle(.segmented)
                
                switch settings.insertionMethod {
                case .clipboard:
                    Text("Text will be copied to the clipboard")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                case .accessibility:
                    if permissionsRepo.currentPermissions.accessibility {
                        Text("Text will be inserted directly into the active application")
                            .font(.caption)
                            .foregroundColor(.green)
                    } else {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Accessibility permission required for direct insertion")
                                .font(.caption)
                                .foregroundColor(.orange)
                            
                            Button("Grant Permission") {
                                Task {
                                    await permissionsRepo.requestAccessibilityPermission()
                                }
                            }
                            .buttonStyle(.link)
                        }
                    }
                }
            }
            .padding(.vertical, 4)
        }
    }
}

struct AdvancedSection: View {
    @Binding var settings: AppSettings
    @State private var isExpanded = false
    
    var body: some View {
        Section("Advanced") {
            DisclosureGroup("AI Processing", isExpanded: $isExpanded) {
                VStack(alignment: .leading, spacing: 12) {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Memory Buffer Size")
                            .font(.headline)
                        
                        HStack {
                            Slider(value: Binding(
                                get: { Double(settings.memoryBufferSize) },
                                set: { settings.memoryBufferSize = Int($0) }
                            ), in: 5...50, step: 1) {
                                Text("Buffer Size")
                            }
                            .frame(maxWidth: 200)
                            
                            Text("\(settings.memoryBufferSize)")
                                .frame(width: 30, alignment: .trailing)
                                .font(.system(.body, design: .monospaced))
                        }
                        
                        Text("Number of previous transcriptions to keep for context")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("AI Temperature")
                            .font(.headline)
                        
                        HStack {
                            Slider(value: $settings.editTemperature, in: 0.0...1.0, step: 0.1) {
                                Text("Temperature")
                            }
                            .frame(maxWidth: 200)
                            
                            Text("\(settings.editTemperature, specifier: "%.1f")")
                                .frame(width: 30, alignment: .trailing)
                                .font(.system(.body, design: .monospaced))
                        }
                        
                        Text("Higher values make AI responses more creative but less predictable")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Max Tokens")
                            .font(.headline)
                        
                        HStack {
                            Slider(value: Binding(
                                get: { Double(settings.editMaxTokens) },
                                set: { settings.editMaxTokens = Int($0) }
                            ), in: 100...1000, step: 50) {
                                Text("Max Tokens")
                            }
                            .frame(maxWidth: 200)
                            
                            Text("\(settings.editMaxTokens)")
                                .frame(width: 40, alignment: .trailing)
                                .font(.system(.body, design: .monospaced))
                        }
                        
                        Text("Maximum length of AI responses")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(.vertical, 4)
        }
    }
}

