import Foundation
import AppKit
import Combine
import Carbon

enum HotkeyEvent: CaseIterable {
    case startVAD           // F7 - Voice Activity Detection
    case toggleRecording    // F8 - Toggle recording
    case startContextual    // F3 - Contextual recording
    case startContextualEdit // F4 - Contextual editing
    case startEdit          // F6 - Edit instruction

    var displayName: String {
        switch self {
        case .startVAD: return "Voice Activity Detection"
        case .toggleRecording: return "Toggle Recording"
        case .startContextual: return "Contextual Recording"
        case .startContextualEdit: return "Contextual Editing"
        case .startEdit: return "Edit Instruction"
        }
    }

    var keyCode: UInt16 {
        switch self {
        case .startVAD: return 103          // F7
        case .toggleRecording: return 100   // F8
        case .startContextual: return 98    // F3
        case .startContextualEdit: return 111 // F4
        case .startEdit: return 105         // F6
        }
    }
}

protocol HotkeyRepository {
    var hotkeyPublisher: AnyPublisher<HotkeyEvent, Never> { get }
    func startMonitoring()
    func stopMonitoring()
}

class NSEventHotkeyRepository: HotkeyRepository {
    private var hotKeyRefs: [EventHotKeyRef?] = []
    private let hotkeySubject = PassthroughSubject<HotkeyEvent, Never>()
    private var eventHandler: EventHandlerRef?
    
    var hotkeyPublisher: AnyPublisher<HotkeyEvent, Never> {
        hotkeySubject.eraseToAnyPublisher()
    }
    
    func startMonitoring() {
        guard eventHandler == nil else { return }
        
        print("🔥 Starting Carbon hotkey monitoring...")
        
        // Install event handler
        var eventType = EventTypeSpec(eventClass: OSType(kEventClassKeyboard), eventKind: OSType(kEventHotKeyPressed))
        let status = InstallEventHandler(GetApplicationEventTarget(), { (nextHandler, theEvent, userData) -> OSStatus in
            guard let userData = userData, let theEvent = theEvent else { return OSStatus(eventNotHandledErr) }
            let repository = Unmanaged<NSEventHotkeyRepository>.fromOpaque(userData).takeUnretainedValue()
            return repository.handleCarbonEvent(theEvent)
        }, 1, &eventType, Unmanaged.passUnretained(self).toOpaque(), &eventHandler)
        
        if status != noErr {
            print("❌ Failed to install event handler: \(status)")
            return
        }
        
        // Register hotkeys
        registerHotkeys()
    }
    
    func stopMonitoring() {
        // Unregister hotkeys
        for hotKeyRef in hotKeyRefs {
            if let ref = hotKeyRef {
                UnregisterEventHotKey(ref)
            }
        }
        hotKeyRefs.removeAll()
        
        // Remove event handler
        if let handler = eventHandler {
            RemoveEventHandler(handler)
            eventHandler = nil
        }
        
        print("🔥 Stopped Carbon hotkey monitoring")
    }
    
    private func registerHotkeys() {
        let hotkeys: [(UInt32, HotkeyEvent)] = [
            (UInt32(kVK_F7), .startVAD),
            (UInt32(kVK_F8), .toggleRecording),
            (UInt32(kVK_F3), .startContextual),
            (UInt32(kVK_F4), .startContextualEdit),
            (UInt32(kVK_F6), .startEdit)
        ]
        
        for (index, (keyCode, event)) in hotkeys.enumerated() {
            var hotKeyRef: EventHotKeyRef?
            let hotKeyID = EventHotKeyID(signature: fourCharCode("DICT"), id: UInt32(index + 1))
            
            let status = RegisterEventHotKey(keyCode, 0, hotKeyID, GetApplicationEventTarget(), 0, &hotKeyRef)
            
            if status == noErr {
                hotKeyRefs.append(hotKeyRef)
                print("✅ Registered hotkey: \(event.displayName)")
            } else {
                hotKeyRefs.append(nil)
                print("❌ Failed to register hotkey \(event.displayName): \(status)")
            }
        }
    }
    
    private func handleCarbonEvent(_ event: EventRef) -> OSStatus {
        print("🔥🔥🔥 CARBON EVENT RECEIVED! 🔥🔥🔥")
        var hotKeyID = EventHotKeyID()
        let status = GetEventParameter(event, OSType(kEventParamDirectObject), OSType(typeEventHotKeyID), nil, MemoryLayout<EventHotKeyID>.size, nil, &hotKeyID)

        guard status == noErr else {
            print("❌ Failed to get hotkey ID from event: \(status)")
            return OSStatus(eventNotHandledErr)
        }

        print("🔥 Hotkey ID received: \(hotKeyID.id)")
        // Map hotkey ID to event
        let hotkeyEvents: [HotkeyEvent] = [.startVAD, .toggleRecording, .startContextual, .startContextualEdit, .startEdit]

        let index = Int(hotKeyID.id) - 1
        guard index >= 0 && index < hotkeyEvents.count else {
            print("❌ Invalid hotkey ID: \(hotKeyID.id)")
            return OSStatus(eventNotHandledErr)
        }

        let hotkeyEvent = hotkeyEvents[index]
        print("🔥 Carbon hotkey detected: \(hotkeyEvent.displayName)")
        print("🔥 Sending hotkey event to main queue...")

        DispatchQueue.main.async {
            print("🔥 Dispatching hotkey event: \(hotkeyEvent.displayName)")
            self.hotkeySubject.send(hotkeyEvent)
        }

        return noErr
    }
    
    deinit {
        stopMonitoring()
    }
}

// Helper for fourCharCode
private func fourCharCode(_ string: String) -> UInt32 {
    let chars = Array(string.utf8)
    return UInt32(chars[0]) << 24 | UInt32(chars[1]) << 16 | UInt32(chars[2]) << 8 | UInt32(chars[3])
}