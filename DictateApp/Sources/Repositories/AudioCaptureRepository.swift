import Foundation
import AVFoundation
import Combine

protocol AudioCaptureRepository {
    func startRecording(mode: RecordingMode) async throws
    func stopRecording() async
    var audioFilePublisher: AnyPublisher<URL, Never> { get }
    var isRecording: Bool { get }
}

class AVFoundationAudioCaptureRepository: NSObject, AudioCaptureRepository, ObservableObject {
    private let audioEngine = AVAudioEngine()
    private let audioFileSubject = PassthroughSubject<URL, Never>()
    private var recordingTimer: Timer?
    private var silenceTimer: Timer?
    private var currentRecordingURL: URL?
    private var audioFile: AVAudioFile?
    
    @Published private(set) var isRecording = false
    
    private let sampleRate: Double = 44100
    private let channels: UInt32 = 1
    private let bitDepth: UInt32 = 16
    
    private var vadManager: VADManager?
    
    var audioFilePublisher: AnyPublisher<URL, Never> {
        audioFileSubject.eraseToAnyPublisher()
    }
    
    override init() {
        super.init()
        vadManager = VADManager(delegate: self)
        setupAudioSession()
    }
    
    private func setupAudioSession() {
        // macOS doesn't use AVAudioSession like iOS
        // Audio session setup is handled automatically by AVAudioEngine
    }
    
    func startRecording(mode: RecordingMode) async throws {
        guard !isRecording else { return }
        
        try await requestMicrophonePermission()
        
        let tempURL = createTemporaryAudioURL()
        currentRecordingURL = tempURL
        
        let inputNode = audioEngine.inputNode
        let recordingFormat = inputNode.outputFormat(forBus: 0)
        
        // Create audio file for recording
        audioFile = try AVAudioFile(forWriting: tempURL, settings: [
            AVFormatIDKey: kAudioFormatLinearPCM,
            AVSampleRateKey: sampleRate,
            AVNumberOfChannelsKey: channels,
            AVLinearPCMBitDepthKey: bitDepth,
            AVLinearPCMIsFloatKey: false,
            AVLinearPCMIsBigEndianKey: false
        ])
        
        // Install tap for recording and VAD
        inputNode.installTap(onBus: 0, bufferSize: 4096, format: recordingFormat) { [weak self] buffer, _ in
            guard let self = self, let audioFile = self.audioFile else { return }
            
            // Write to file
            do {
                try audioFile.write(from: buffer)
            } catch {
                print("Failed to write audio buffer: \(error)")
            }
            
            // Send to VAD for processing
            if mode == .voiceActivity {
                self.vadManager?.processAudioBuffer(buffer)
            }
        }
        
        try audioEngine.start()
        isRecording = true
        
        // Handle different recording modes
        switch mode {
        case .voiceActivity:
            vadManager?.startVAD()
        case .toggle, .contextual, .edit, .contextualEdit:
            // Manual stop required
            break
        }
    }
    
    func stopRecording() async {
        guard isRecording else { return }
        
        recordingTimer?.invalidate()
        recordingTimer = nil
        
        silenceTimer?.invalidate()
        silenceTimer = nil
        
        vadManager?.stopVAD()
        
        audioEngine.inputNode.removeTap(onBus: 0)
        audioEngine.stop()
        
        audioFile = nil
        isRecording = false
        
        if let url = currentRecordingURL {
            audioFileSubject.send(url)
            currentRecordingURL = nil
        }
    }
    
    private func requestMicrophonePermission() async throws {
        // On macOS, we use AVCaptureDevice for microphone permissions
        let status = AVCaptureDevice.authorizationStatus(for: .audio)
        
        switch status {
        case .authorized:
            return
        case .denied, .restricted:
            throw PermissionError.microphoneNotAuthorized
        case .notDetermined:
            let granted = await AVCaptureDevice.requestAccess(for: .audio)
            if !granted {
                throw PermissionError.microphoneNotAuthorized
            }
        @unknown default:
            throw PermissionError.systemError("Unknown microphone permission status")
        }
    }
    
    private func createTemporaryAudioURL() -> URL {
        let tempDir = FileManager.default.temporaryDirectory
        let filename = "recording_\(Date().timeIntervalSince1970).wav"
        return tempDir.appendingPathComponent(filename)
    }
}

// MARK: - VAD Manager Delegate
extension AVFoundationAudioCaptureRepository: VADManagerDelegate {
    func vadDidDetectSpeechStart() {
        // Reset silence timer when speech is detected
        silenceTimer?.invalidate()
        silenceTimer = nil
    }
    
    func vadDidDetectSpeechEnd() {
        // Start silence timer
        silenceTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: false) { [weak self] _ in
            Task { await self?.stopRecording() }
        }
    }
}