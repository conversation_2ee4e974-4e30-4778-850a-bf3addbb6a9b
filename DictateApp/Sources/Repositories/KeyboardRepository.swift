import Foundation
import AppKit
import Carbon

protocol KeyboardRepository {
    func typeText(_ text: String)
    func selectAndReplaceText(originalText: String, newText: String)
}

class CGEventKeyboardRepository: KeyboardRepository {
    
    func typeText(_ text: String) {
        print("🚨🚨🚨 KEYBOARD REPOSITORY typeText CALLED! 🚨🚨🚨")
        print("⌨️ Text to type: '\(text)'")
        print("⌨️ Text length: \(text.count)")
        print("⌨️ Text is empty: \(text.isEmpty)")

        if text.isEmpty {
            print("⚠️ Text is empty, nothing to type")
            return
        }

        // Check if we have permission to create events
        guard checkAccessibilityPermissions() else {
            print("❌ No accessibility permissions for keystroke injection")
            return
        }

        print("✅ Accessibility permissions OK, proceeding to type...")

        // Check what application is currently focused
        if let frontmostApp = NSWorkspace.shared.frontmostApplication {
            print("🎯 Frontmost application: \(frontmostApp.localizedName ?? "Unknown") (PID: \(frontmostApp.processIdentifier))")
        } else {
            print("⚠️ Could not determine frontmost application")
        }

        // Add a small delay before starting to type to ensure focus is stable
        print("⏱️ Adding 100ms delay before typing...")
        usleep(100000) // 100ms

        for (index, character) in text.enumerated() {
            print("⌨️ Typing character \(index + 1)/\(text.count): '\(character)'")

            if character == "\n" {
                // Send Return key for newlines
                print("⌨️ Sending Return key for newline")
                sendKeyEvent(keyCode: CGKeyCode(kVK_Return))
            } else {
                // Use CGEvent for character typing
                typeCharacter(character)
            }

            // Increase delay to mimic more human-like typing
            usleep(50000) // 50ms
        }

        print("✅ Finished typing all characters")
    }
    
    private func checkAccessibilityPermissions() -> Bool {
        let trusted = AXIsProcessTrusted()
        if !trusted {
            print("❌ App needs Accessibility permissions for keystroke injection")
            print("📋 Go to System Preferences > Security & Privacy > Privacy > Accessibility")
            print("📋 Add 'DictateApp' to the list and enable it")
        }
        return trusted
    }
    
    func selectAndReplaceText(originalText: String, newText: String) {
        guard !originalText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            print("⚠️ Original text is empty, just typing new text")
            typeText(newText)
            return
        }
        
        print("✏️ Replacing: '\(originalText)' → '\(newText)'")
        
        // Calculate word count for selection
        let wordCount = originalText.split(separator: " ").count
        
        // Select text by moving word by word backwards
        for _ in 0..<wordCount {
            // Cmd+Shift+Left Arrow to select previous word
            sendModifierKeyEvent(keyCode: CGKeyCode(kVK_LeftArrow), 
                               modifiers: [.maskCommand, .maskShift])
            usleep(10000) // 10ms delay
        }
        
        // Delete selected text
        sendKeyEvent(keyCode: CGKeyCode(kVK_Delete))
        usleep(50000) // 50ms delay
        
        // Type new text
        typeText(newText)
    }
    
    private func typeCharacter(_ character: Character) {
        let string = String(character)
        print("🔤 Attempting to type character: '\(character)' (Unicode: \(string.unicodeScalars.map { $0.value }))")

        // Create an event source for better control
        let eventSource = CGEventSource(stateID: .hidSystemState)

        // Convert string to UniChar array
        let unicodeString = string.utf16.map { $0 }

        // Create key down event with event source
        guard let keyDownEvent = CGEvent(keyboardEventSource: eventSource,
                                       virtualKey: 0,
                                       keyDown: true) else {
            print("❌ Failed to create key down event for '\(character)'")
            return
        }

        // Set the unicode string for the event
        unicodeString.withUnsafeBufferPointer { buffer in
            keyDownEvent.keyboardSetUnicodeString(stringLength: unicodeString.count,
                                                unicodeString: buffer.baseAddress!)
        }

        // Set event flags to ensure it's treated as a normal key event
        keyDownEvent.flags = []

        // Post to the annotation session event tap for better compatibility
        print("📤 Posting key down event for '\(character)' to annotation session")
        keyDownEvent.post(tap: .cgAnnotatedSessionEventTap)

        // Create key up event with event source
        guard let keyUpEvent = CGEvent(keyboardEventSource: eventSource,
                                     virtualKey: 0,
                                     keyDown: false) else {
            print("❌ Failed to create key up event for '\(character)'")
            return
        }

        // Set the unicode string for the event
        unicodeString.withUnsafeBufferPointer { buffer in
            keyUpEvent.keyboardSetUnicodeString(stringLength: unicodeString.count,
                                              unicodeString: buffer.baseAddress!)
        }

        // Set event flags to ensure it's treated as a normal key event
        keyUpEvent.flags = []

        print("📤 Posting key up event for '\(character)' to annotation session")
        keyUpEvent.post(tap: .cgAnnotatedSessionEventTap)
    }
    
    private func sendKeyEvent(keyCode: CGKeyCode) {
        print("🔑 Sending key event for keyCode: \(keyCode)")

        // Create an event source for better control
        let eventSource = CGEventSource(stateID: .hidSystemState)

        // Key down
        if let keyDownEvent = CGEvent(keyboardEventSource: eventSource,
                                    virtualKey: keyCode,
                                    keyDown: true) {
            keyDownEvent.flags = []
            print("📤 Posting key down event for keyCode: \(keyCode)")
            keyDownEvent.post(tap: .cgAnnotatedSessionEventTap)
        }

        // Key up
        if let keyUpEvent = CGEvent(keyboardEventSource: eventSource,
                                  virtualKey: keyCode,
                                  keyDown: false) {
            keyUpEvent.flags = []
            print("📤 Posting key up event for keyCode: \(keyCode)")
            keyUpEvent.post(tap: .cgAnnotatedSessionEventTap)
        }
    }
    
    private func sendModifierKeyEvent(keyCode: CGKeyCode, modifiers: [CGEventFlags]) {
        let flags = CGEventFlags(modifiers)
        
        // Key down with modifiers
        if let keyDownEvent = CGEvent(keyboardEventSource: nil, 
                                    virtualKey: keyCode, 
                                    keyDown: true) {
            keyDownEvent.flags = flags
            keyDownEvent.post(tap: .cghidEventTap)
        }
        
        // Key up with modifiers
        if let keyUpEvent = CGEvent(keyboardEventSource: nil, 
                                  virtualKey: keyCode, 
                                  keyDown: false) {
            keyUpEvent.flags = flags
            keyUpEvent.post(tap: .cghidEventTap)
        }
    }
}

// Extension to help with modifier flag combinations
extension CGEventFlags {
    init(_ flags: [CGEventFlags]) {
        var combinedFlags: CGEventFlags = []
        for flag in flags {
            combinedFlags.insert(flag)
        }
        self = combinedFlags
    }
}