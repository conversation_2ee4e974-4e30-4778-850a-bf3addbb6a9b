{"": {"swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/master.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/BashCompletionsGenerator.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/BashCompletionsGenerator.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/BashCompletionsGenerator.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/BashCompletionsGenerator~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/BashCompletionsGenerator.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/CompletionsGenerator.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionsGenerator.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionsGenerator.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionsGenerator~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionsGenerator.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/FishCompletionsGenerator.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/FishCompletionsGenerator.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/FishCompletionsGenerator.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/FishCompletionsGenerator~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/FishCompletionsGenerator.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/ZshCompletionsGenerator.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ZshCompletionsGenerator.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ZshCompletionsGenerator.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ZshCompletionsGenerator~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ZshCompletionsGenerator.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Argument.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Argument.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Argument.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Argument~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Argument.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentHelp.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentHelp.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentHelp.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentHelp~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentHelp.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentVisibility.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentVisibility.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentVisibility.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentVisibility~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentVisibility.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/CompletionKind.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionKind.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionKind.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionKind~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/CompletionKind.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Errors.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Errors.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Errors.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Errors~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Errors.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Flag.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Flag.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Flag.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Flag~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Flag.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/NameSpecification.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/NameSpecification.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/NameSpecification.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/NameSpecification~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/NameSpecification.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Option.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Option.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Option.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Option~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Option.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/OptionGroup.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/OptionGroup.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/OptionGroup.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/OptionGroup~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/OptionGroup.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/AsyncParsableCommand.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/AsyncParsableCommand.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/AsyncParsableCommand.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/AsyncParsableCommand~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/AsyncParsableCommand.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/CommandConfiguration.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandConfiguration.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandConfiguration.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandConfiguration~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandConfiguration.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/CommandGroup.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandGroup.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandGroup.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandGroup~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandGroup.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/EnumerableFlag.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/EnumerableFlag.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/EnumerableFlag.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/EnumerableFlag~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/EnumerableFlag.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ExpressibleByArgument.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ExpressibleByArgument.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ExpressibleByArgument.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ExpressibleByArgument~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ExpressibleByArgument.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArguments.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArguments.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArguments.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArguments~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArguments.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArgumentsValidation.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArgumentsValidation.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArgumentsValidation.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArgumentsValidation~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableArgumentsValidation.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableCommand.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableCommand.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableCommand.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableCommand~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsableCommand.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDecoder.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDecoder.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDecoder.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDecoder~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDecoder.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDefinition.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDefinition.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDefinition.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDefinition~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentDefinition.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentSet.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentSet.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentSet.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentSet~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ArgumentSet.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/CommandParser.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandParser.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandParser.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandParser~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/CommandParser.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputKey.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputKey.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputKey.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputKey~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputKey.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputOrigin.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputOrigin.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputOrigin.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputOrigin~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/InputOrigin.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Name.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Name.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Name.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Name~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Name.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Parsed.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Parsed.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Parsed.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Parsed~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Parsed.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParsedValues.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsedValues.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsedValues.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsedValues~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParsedValues.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParserError.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParserError.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParserError.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParserError~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/ParserError.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/SplitArguments.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/SplitArguments.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/SplitArguments.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/SplitArguments~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/SplitArguments.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/DumpHelpGenerator.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/DumpHelpGenerator.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/DumpHelpGenerator.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/DumpHelpGenerator~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/DumpHelpGenerator.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpCommand.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpCommand.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpCommand.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpCommand~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpCommand.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpGenerator.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpGenerator.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpGenerator.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpGenerator~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/HelpGenerator.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/MessageInfo.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/MessageInfo.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/MessageInfo.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/MessageInfo~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/MessageInfo.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/UsageGenerator.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/UsageGenerator.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/UsageGenerator.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/UsageGenerator~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/UsageGenerator.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/CollectionExtensions.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/CollectionExtensions.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/CollectionExtensions.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/CollectionExtensions~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/CollectionExtensions.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Platform.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Platform.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Platform.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Platform~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Platform.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/SequenceExtensions.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/SequenceExtensions.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/SequenceExtensions.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/SequenceExtensions~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/SequenceExtensions.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/StringExtensions.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/StringExtensions.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/StringExtensions.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/StringExtensions~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/StringExtensions.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Tree.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Tree.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Tree.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Tree~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser.build/Tree.swiftdeps"}}