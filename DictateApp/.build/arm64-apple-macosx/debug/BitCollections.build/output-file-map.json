{"": {"swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/master.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+BitwiseOperations.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+BitwiseOperations.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+BitwiseOperations.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+BitwiseOperations~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+BitwiseOperations.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+ChunkedBitsIterators.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+ChunkedBitsIterators.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+ChunkedBitsIterators.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+ChunkedBitsIterators~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+ChunkedBitsIterators.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+Codable.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Codable.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Codable.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Codable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Codable.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+Collection.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Collection.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Collection.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Collection~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Collection.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+Copy.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Copy.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Copy.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Copy~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Copy.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+CustomReflectable.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+CustomReflectable.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+CustomReflectable.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+CustomReflectable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+CustomReflectable.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+Descriptions.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Descriptions.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Descriptions.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Descriptions~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Descriptions.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+Equatable.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Equatable.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Equatable.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Equatable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Equatable.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+ExpressibleByArrayLiteral.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+ExpressibleByArrayLiteral.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+ExpressibleByArrayLiteral.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+ExpressibleByArrayLiteral~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+ExpressibleByArrayLiteral.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+ExpressibleByStringLiteral.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+ExpressibleByStringLiteral.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+ExpressibleByStringLiteral.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+ExpressibleByStringLiteral~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+ExpressibleByStringLiteral.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+Extras.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Extras.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Extras.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Extras~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Extras.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+Fill.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Fill.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Fill.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Fill~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Fill.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+Hashable.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Hashable.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Hashable.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Hashable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Hashable.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+Initializers.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Initializers.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Initializers.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Initializers~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Initializers.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+Invariants.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Invariants.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Invariants.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Invariants~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Invariants.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+LosslessStringConvertible.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+LosslessStringConvertible.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+LosslessStringConvertible.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+LosslessStringConvertible~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+LosslessStringConvertible.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+RandomBits.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+RandomBits.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+RandomBits.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+RandomBits~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+RandomBits.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+RangeReplaceableCollection.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+RangeReplaceableCollection.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+RangeReplaceableCollection.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+RangeReplaceableCollection~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+RangeReplaceableCollection.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+Shifts.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Shifts.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Shifts.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Shifts~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Shifts.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray+Testing.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Testing.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Testing.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Testing~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray+Testing.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray._UnsafeHandle.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray._UnsafeHandle.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray._UnsafeHandle.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray._UnsafeHandle~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray._UnsafeHandle.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitArray/BitArray.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitArray.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+BidirectionalCollection.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+BidirectionalCollection.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+BidirectionalCollection.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+BidirectionalCollection~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+BidirectionalCollection.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+Codable.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Codable.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Codable.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Codable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Codable.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+CustomDebugStringConvertible.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+CustomDebugStringConvertible.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+CustomDebugStringConvertible.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+CustomDebugStringConvertible~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+CustomDebugStringConvertible.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+CustomReflectable.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+CustomReflectable.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+CustomReflectable.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+CustomReflectable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+CustomReflectable.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+CustomStringConvertible.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+CustomStringConvertible.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+CustomStringConvertible.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+CustomStringConvertible~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+CustomStringConvertible.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+Equatable.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Equatable.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Equatable.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Equatable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Equatable.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+ExpressibleByArrayLiteral.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+ExpressibleByArrayLiteral.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+ExpressibleByArrayLiteral.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+ExpressibleByArrayLiteral~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+ExpressibleByArrayLiteral.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+Extras.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Extras.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Extras.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Extras~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Extras.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+Hashable.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Hashable.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Hashable.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Hashable~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Hashable.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+Initializers.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Initializers.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Initializers.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Initializers~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Initializers.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+Invariants.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Invariants.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Invariants.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Invariants~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Invariants.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+Random.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Random.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Random.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Random~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Random.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra basics.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra basics.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra basics.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra basics~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra basics.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra conformance.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra conformance.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra conformance.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra conformance~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra conformance.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra formIntersection.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra formIntersection.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra formIntersection.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra formIntersection~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra formIntersection.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra formSymmetricDifference.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra formSymmetricDifference.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra formSymmetricDifference.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra formSymmetricDifference~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra formSymmetricDifference.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra formUnion.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra formUnion.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra formUnion.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra formUnion~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra formUnion.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra intersection.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra intersection.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra intersection.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra intersection~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra intersection.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra isDisjoint.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra isDisjoint.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra isDisjoint.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra isDisjoint~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra isDisjoint.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra isEqualSet.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra isEqualSet.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra isEqualSet.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra isEqualSet~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra isEqualSet.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra isStrictSubset.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra isStrictSubset.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra isStrictSubset.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra isStrictSubset~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra isStrictSubset.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra isStrictSuperset.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra isStrictSuperset.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra isStrictSuperset.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra isStrictSuperset~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra isStrictSuperset.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra isSubset.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra isSubset.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra isSubset.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra isSubset~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra isSubset.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra isSuperset.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra isSuperset.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra isSuperset.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra isSuperset~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra isSuperset.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra subtract.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra subtract.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra subtract.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra subtract~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra subtract.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra subtracting.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra subtracting.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra subtracting.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra subtracting~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra subtracting.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra symmetricDifference.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra symmetricDifference.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra symmetricDifference.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra symmetricDifference~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra symmetricDifference.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+SetAlgebra union.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra union.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra union.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra union~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+SetAlgebra union.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet+Sorted Collection APIs.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Sorted Collection APIs.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Sorted Collection APIs.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Sorted Collection APIs~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet+Sorted Collection APIs.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet.Counted.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet.Counted.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet.Counted.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet.Counted~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet.Counted.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet.Index.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet.Index.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet.Index.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet.Index~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet.Index.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet._UnsafeHandle.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet._UnsafeHandle.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet._UnsafeHandle.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet._UnsafeHandle~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet._UnsafeHandle.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/BitSet/BitSet.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/BitSet.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/Shared/Range+Utilities.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/Range+Utilities.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/Range+Utilities.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/Range+Utilities~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/Range+Utilities.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/Shared/Slice+Utilities.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/Slice+Utilities.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/Slice+Utilities.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/Slice+Utilities~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/Slice+Utilities.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/Shared/UInt+Tricks.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/UInt+Tricks.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/UInt+Tricks.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/UInt+Tricks~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/UInt+Tricks.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/BitCollections/Shared/_Word.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/_Word.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/_Word.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/_Word~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/BitCollections.build/_Word.swiftdeps"}}