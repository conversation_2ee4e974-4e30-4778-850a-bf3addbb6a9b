{"": {"swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/master.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParserToolInfo/ToolInfo.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/ToolInfo.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/ToolInfo.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/ToolInfo~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParserToolInfo.build/ToolInfo.swiftdeps"}}