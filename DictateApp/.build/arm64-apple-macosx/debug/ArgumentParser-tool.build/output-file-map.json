{"": {"swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/master.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/BashCompletionsGenerator.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/BashCompletionsGenerator.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/BashCompletionsGenerator.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/BashCompletionsGenerator~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/BashCompletionsGenerator.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/CompletionsGenerator.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CompletionsGenerator.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CompletionsGenerator.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CompletionsGenerator~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CompletionsGenerator.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/FishCompletionsGenerator.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/FishCompletionsGenerator.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/FishCompletionsGenerator.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/FishCompletionsGenerator~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/FishCompletionsGenerator.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Completions/ZshCompletionsGenerator.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ZshCompletionsGenerator.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ZshCompletionsGenerator.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ZshCompletionsGenerator~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ZshCompletionsGenerator.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Argument.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Argument.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Argument.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Argument~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Argument.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentHelp.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentHelp.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentHelp.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentHelp~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentHelp.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/ArgumentVisibility.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentVisibility.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentVisibility.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentVisibility~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentVisibility.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/CompletionKind.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CompletionKind.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CompletionKind.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CompletionKind~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CompletionKind.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Errors.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Errors.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Errors.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Errors~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Errors.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Flag.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Flag.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Flag.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Flag~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Flag.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/NameSpecification.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/NameSpecification.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/NameSpecification.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/NameSpecification~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/NameSpecification.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/Option.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Option.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Option.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Option~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Option.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Properties/OptionGroup.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/OptionGroup.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/OptionGroup.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/OptionGroup~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/OptionGroup.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/AsyncParsableCommand.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/AsyncParsableCommand.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/AsyncParsableCommand.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/AsyncParsableCommand~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/AsyncParsableCommand.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/CommandConfiguration.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CommandConfiguration.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CommandConfiguration.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CommandConfiguration~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CommandConfiguration.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/CommandGroup.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CommandGroup.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CommandGroup.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CommandGroup~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CommandGroup.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/EnumerableFlag.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/EnumerableFlag.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/EnumerableFlag.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/EnumerableFlag~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/EnumerableFlag.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ExpressibleByArgument.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ExpressibleByArgument.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ExpressibleByArgument.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ExpressibleByArgument~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ExpressibleByArgument.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArguments.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableArguments.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableArguments.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableArguments~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableArguments.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableArgumentsValidation.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableArgumentsValidation.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableArgumentsValidation.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableArgumentsValidation~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableArgumentsValidation.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsable Types/ParsableCommand.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableCommand.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableCommand.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableCommand~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsableCommand.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDecoder.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentDecoder.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentDecoder.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentDecoder~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentDecoder.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentDefinition.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentDefinition.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentDefinition.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentDefinition~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentDefinition.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ArgumentSet.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentSet.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentSet.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentSet~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ArgumentSet.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/CommandParser.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CommandParser.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CommandParser.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CommandParser~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CommandParser.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputKey.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/InputKey.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/InputKey.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/InputKey~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/InputKey.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/InputOrigin.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/InputOrigin.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/InputOrigin.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/InputOrigin~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/InputOrigin.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Name.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Name.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Name.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Name~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Name.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/Parsed.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Parsed.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Parsed.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Parsed~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Parsed.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParsedValues.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsedValues.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsedValues.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsedValues~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParsedValues.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/ParserError.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParserError.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParserError.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParserError~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/ParserError.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Parsing/SplitArguments.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/SplitArguments.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/SplitArguments.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/SplitArguments~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/SplitArguments.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/DumpHelpGenerator.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/DumpHelpGenerator.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/DumpHelpGenerator.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/DumpHelpGenerator~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/DumpHelpGenerator.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpCommand.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/HelpCommand.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/HelpCommand.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/HelpCommand~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/HelpCommand.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/HelpGenerator.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/HelpGenerator.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/HelpGenerator.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/HelpGenerator~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/HelpGenerator.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/MessageInfo.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/MessageInfo.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/MessageInfo.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/MessageInfo~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/MessageInfo.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Usage/UsageGenerator.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/UsageGenerator.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/UsageGenerator.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/UsageGenerator~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/UsageGenerator.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/CollectionExtensions.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CollectionExtensions.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CollectionExtensions.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CollectionExtensions~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/CollectionExtensions.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Platform.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Platform.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Platform.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Platform~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Platform.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/SequenceExtensions.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/SequenceExtensions.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/SequenceExtensions.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/SequenceExtensions~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/SequenceExtensions.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/StringExtensions.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/StringExtensions.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/StringExtensions.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/StringExtensions~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/StringExtensions.swiftdeps"}, "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Sources/ArgumentParser/Utilities/Tree.swift": {"dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Tree.d", "object": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Tree.swift.o", "swiftmodule": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Tree~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/debug/ArgumentParser-tool.build/Tree.swiftdeps"}}