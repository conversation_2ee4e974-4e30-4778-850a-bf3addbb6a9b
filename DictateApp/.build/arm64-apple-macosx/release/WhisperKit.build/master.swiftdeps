version: "Apple Swift version 6.1.2 (swiftlang-6.1.2.1.2 clang-1700.0.13.5)"
options: "0a7d5809e5f13cad0f52c03df209d006e76b686c445a4986a6ae93671ce90da5"
build_start_time: [1750241056, 795473000]
build_end_time: [1750241062, 112635000]
inputs:
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/WhisperKit/Sources/WhisperKit/Core/Audio/AudioChunker.swift"
  : [1750229487, 397791533]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/WhisperKit/Sources/WhisperKit/Core/Audio/AudioProcessor.swift"
  : [1750229487, 397886367]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/WhisperKit/Sources/WhisperKit/Core/Audio/AudioStreamTranscriber.swift"
  : [1750229487, 397959701]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/WhisperKit/Sources/WhisperKit/Core/Audio/EnergyVAD.swift"
  : [1750229487, 398050411]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/WhisperKit/Sources/WhisperKit/Core/Audio/VoiceActivityDetector.swift"
  : [1750229487, 398122995]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/WhisperKit/Sources/WhisperKit/Core/AudioEncoder.swift"
  : [1750229487, 398202747]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/WhisperKit/Sources/WhisperKit/Core/Configurations.swift"
  : [1750229487, 398264706]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/WhisperKit/Sources/WhisperKit/Core/FeatureExtractor.swift"
  : [1750229487, 398318707]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/WhisperKit/Sources/WhisperKit/Core/Models.swift"
  : [1750229487, 398454417]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/WhisperKit/Sources/WhisperKit/Core/Text/LogitsFilter.swift"
  : [1750229487, 398556876]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/WhisperKit/Sources/WhisperKit/Core/Text/SegmentSeeker.swift"
  : [1750229487, 398630086]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/WhisperKit/Sources/WhisperKit/Core/Text/TokenSampler.swift"
  : [1750229487, 398718046]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/WhisperKit/Sources/WhisperKit/Core/TextDecoder.swift"
  : [1750229487, 398801505]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/WhisperKit/Sources/WhisperKit/Core/TranscribeTask.swift"
  : [1750229487, 398869839]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/WhisperKit/Sources/WhisperKit/Core/WhisperKit.swift"
  : [1750229487, 399007008]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/WhisperKit/Sources/WhisperKit/Utilities/Concurrency.swift"
  : [1750229487, 399098468]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/WhisperKit/Sources/WhisperKit/Utilities/Extensions+Internal.swift"
  : [1750229487, 399165302]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/WhisperKit/Sources/WhisperKit/Utilities/Extensions+Public.swift"
  : [1750229487, 399227511]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/WhisperKit/Sources/WhisperKit/Utilities/Logging.swift"
  : [1750229487, 399285720]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/WhisperKit/Sources/WhisperKit/Utilities/ModelUtilities.swift"
  : [1750229487, 399338096]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/WhisperKit/Sources/WhisperKit/Utilities/ResultWriter.swift"
  : [1750229487, 399430014]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/WhisperKit/Sources/WhisperKit/Utilities/TextUtilities.swift"
  : [1750229487, 399490431]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/WhisperKit/Sources/WhisperKit/Utilities/TranscriptionUtilities.swift"
  : [1750229487, 399556224]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/WhisperKit/Sources/WhisperKit/Utilities/WhisperError.swift"
  : [1750229487, 399608975]
