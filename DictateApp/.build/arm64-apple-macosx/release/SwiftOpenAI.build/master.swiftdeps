version: "Apple Swift version 6.1.2 (swiftlang-*******.2 clang-1700.0.13.5)"
options: "ae6f661ed03f428c90a580392705ece17efa1608b0954097692824dc0aded9c4"
build_start_time: [1750241047, 736639000]
build_end_time: [1750241053, 937952000]
inputs:
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/AIProxy/AIProxyCertificatePinning.swift"
  : [1750228575, 980241051]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/AIProxy/AIProxyService.swift"
  : [1750228575, 980413758]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/AIProxy/Endpoint+AIProxy.swift"
  : [1750228575, 980490049]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Azure/AzureOpenAIAPI.swift"
  : [1750228575, 980710340]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Azure/AzureOpenAIConfiguration.swift"
  : [1750228575, 980783339]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Azure/DefaultOpenAIAzureService.swift"
  : [1750228575, 980955421]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/LocalModelService/LocalModelAPI.swift"
  : [1750228575, 981073546]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/LocalModelService/LocalModelService.swift"
  : [1750228575, 981134420]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Private/Networking/Endpoint.swift"
  : [1750228575, 981258045]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Private/Networking/MultipartFormDataBuilder.swift"
  : [1750228575, 981312586]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Private/Networking/OpenAIAPI.swift"
  : [1750228575, 981420502]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Parameters/Assistant/AssistantParameters.swift"
  : [1750228575, 981874958]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Parameters/Audio/AudioSpeechParameters.swift"
  : [1750228575, 981981790]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Parameters/Audio/AudioTranscriptionParameters.swift"
  : [1750228575, 982190289]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Parameters/Audio/AudioTranslationParameters.swift"
  : [1750228575, 982246747]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Parameters/Batch/BatchParameter.swift"
  : [1750228575, 982342247]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Parameters/Chat/ChatCompletionParameters.swift"
  : [1750228575, 982457121]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Parameters/Embedding/EmbeddingParameter.swift"
  : [1750228575, 982560204]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Parameters/File/FileParameter.swift"
  : [1750228575, 982664286]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Parameters/FineTuning/FineTuningJobParameters.swift"
  : [1750228575, 982770161]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Parameters/Image/Dalle.swift"
  : [1750228575, 982864618]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Parameters/Image/ImageCreateParameters.swift"
  : [1750228575, 982931410]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Parameters/Image/ImageEditParameters.swift"
  : [1750228575, 983022118]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Parameters/Image/ImageVariationParameters.swift"
  : [1750228575, 983084867]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Parameters/Message/MessageParameter.swift"
  : [1750228575, 983219866]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Parameters/Message/ModifyMessageParameters.swift"
  : [1750228575, 983290116]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Parameters/Model.swift"
  : [1750228575, 983367574]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Parameters/Moderation/ModerationParameter.swift"
  : [1750228575, 983469990]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Parameters/MultipartFormDataParameters.swift"
  : [1750228575, 983525531]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Parameters/Runs/CreateThreadAndRunParameter.swift"
  : [1750228575, 983657614]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Parameters/Runs/ModifyRunParameters.swift"
  : [1750228575, 983708780]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Parameters/Runs/RunParameter.swift"
  : [1750228575, 983761738]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Parameters/Runs/RunToolsOutputParameter.swift"
  : [1750228575, 983817780]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Parameters/Threads/CreateThreadParameters.swift"
  : [1750228575, 983934446]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Parameters/Threads/ModifyThreadParameters.swift"
  : [1750228575, 983991570]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Parameters/VectorStore/VectorStoreParameter.swift"
  : [1750228575, 984104111]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Parameters/VectorStoreFileBatch/VectorStoreFileBatchParameter.swift"
  : [1750228575, 984199611]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Parameters/VectorStoreFiles/VectorStoreFileParameter.swift"
  : [1750228575, 984294943]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/Assistants/AssistantObject.swift"
  : [1750228575, 984446609]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/Assistants/AssistantStreamEvent.swift"
  : [1750228575, 984509526]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/Assistants/AssistantStreamEventObject.swift"
  : [1750228575, 984575650]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/Audio/AudioObject.swift"
  : [1750228575, 984675358]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/Audio/AudioSpeechObject.swift"
  : [1750228575, 984741524]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/Batch/BatchObject.swift"
  : [1750228575, 984851940]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/Chat/ChatCompletionChunkObject.swift"
  : [1750228575, 984973939]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/Chat/ChatCompletionObject.swift"
  : [1750228575, 985041647]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/Chat/ChatUsage.swift"
  : [1750228575, 985106147]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/Delta.swift"
  : [1750228575, 985167938]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/Embedding/EmbeddingObject.swift"
  : [1750228575, 985272938]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/File/FileObject.swift"
  : [1750228575, 985376395]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/FineTuning/FineTuningJobEventObject.swift"
  : [1750228575, 985501895]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/FineTuning/FineTuningJobObject.swift"
  : [1750228575, 985592436]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/Image/ImageObject.swift"
  : [1750228575, 985693019]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/Messages/MessageContent.swift"
  : [1750228575, 985810309]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/Messages/MessageDeltaObject.swift"
  : [1750228575, 985901184]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/Messages/MessageObject.swift"
  : [1750228575, 985972100]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/Model/ModelObject.swift"
  : [1750228575, 986098683]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/Moderation/ModerationObject.swift"
  : [1750228575, 986236599]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/OpenAIErrorResponse.swift"
  : [1750228575, 986308390]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/OpenAIResponse.swift"
  : [1750228575, 986380348]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/Runs/RunObject.swift"
  : [1750228575, 986507430]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/Runs/RunStepDeltaObject.swift"
  : [1750228575, 986595347]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/Runs/RunStepDetails.swift"
  : [1750228575, 986850178]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/Runs/RunStepObject.swift"
  : [1750228575, 986917595]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/Threads/ThreadObject.swift"
  : [1750228575, 987025219]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/VectorStore/VectorStoreObject.swift"
  : [1750228575, 987147093]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/VectorStoreFile/VectorStoreFileObject.swift"
  : [1750228575, 987258551]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/ResponseModels/VectorStoreFileBatch/VectorStoreFileBatchObject.swift"
  : [1750228575, 987370592]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Service/DefaultOpenAIService.swift"
  : [1750228575, 987517841]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Service/OpenAIService.swift"
  : [1750228575, 987678257]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Service/OpenAIServiceFactory.swift"
  : [1750228575, 987767548]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Shared/DeletionStatus.swift"
  : [1750228575, 987879631]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Shared/ExpirationPolicy.swift"
  : [1750228575, 987952422]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Shared/FileCount.swift"
  : [1750228575, 988022963]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Shared/IncompleteDetails.swift"
  : [1750228575, 988090629]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Shared/JSONSchema.swift"
  : [1750228575, 988160962]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Shared/LastError.swift"
  : [1750228575, 988219795]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Shared/MessageAttachment.swift"
  : [1750228575, 988277378]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Shared/ResponseFormat.swift"
  : [1750228575, 988344378]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Shared/ToolCall.swift"
  : [1750228575, 988403461]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Shared/ToolChoice.swift"
  : [1750228575, 988463877]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Shared/ToolResources.swift"
  : [1750228575, 988543710]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Shared/TruncationStrategy.swift"
  : [1750228575, 988608668]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/SwiftOpenAI/Sources/OpenAI/Public/Shared/Usage.swift"
  : [1750228575, 988680459]
