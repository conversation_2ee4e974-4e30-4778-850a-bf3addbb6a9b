/Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/FixedWidthInteger+roundUpToPowerOfTwo.swift.o : /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+reversed.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+_Word.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Debugging.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/Integer\ rank.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_SortedCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_UniqueCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/FixedWidthInteger+roundUpToPowerOfTwo.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeMutableBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Descriptions.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/RandomAccessCollection+Offsets.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+first\ and\ last\ set\ bit.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+Index.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/Integer\ rank.swift.o : /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+reversed.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+_Word.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Debugging.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/Integer\ rank.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_SortedCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_UniqueCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/FixedWidthInteger+roundUpToPowerOfTwo.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeMutableBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Descriptions.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/RandomAccessCollection+Offsets.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+first\ and\ last\ set\ bit.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+Index.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/UInt+first\ and\ last\ set\ bit.swift.o : /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+reversed.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+_Word.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Debugging.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/Integer\ rank.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_SortedCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_UniqueCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/FixedWidthInteger+roundUpToPowerOfTwo.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeMutableBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Descriptions.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/RandomAccessCollection+Offsets.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+first\ and\ last\ set\ bit.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+Index.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/UInt+reversed.swift.o : /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+reversed.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+_Word.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Debugging.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/Integer\ rank.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_SortedCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_UniqueCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/FixedWidthInteger+roundUpToPowerOfTwo.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeMutableBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Descriptions.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/RandomAccessCollection+Offsets.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+first\ and\ last\ set\ bit.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+Index.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/_UnsafeBitSet+Index.swift.o : /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+reversed.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+_Word.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Debugging.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/Integer\ rank.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_SortedCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_UniqueCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/FixedWidthInteger+roundUpToPowerOfTwo.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeMutableBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Descriptions.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/RandomAccessCollection+Offsets.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+first\ and\ last\ set\ bit.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+Index.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/_UnsafeBitSet+_Word.swift.o : /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+reversed.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+_Word.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Debugging.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/Integer\ rank.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_SortedCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_UniqueCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/FixedWidthInteger+roundUpToPowerOfTwo.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeMutableBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Descriptions.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/RandomAccessCollection+Offsets.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+first\ and\ last\ set\ bit.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+Index.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/_UnsafeBitSet.swift.o : /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+reversed.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+_Word.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Debugging.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/Integer\ rank.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_SortedCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_UniqueCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/FixedWidthInteger+roundUpToPowerOfTwo.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeMutableBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Descriptions.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/RandomAccessCollection+Offsets.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+first\ and\ last\ set\ bit.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+Index.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/_SortedCollection.swift.o : /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+reversed.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+_Word.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Debugging.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/Integer\ rank.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_SortedCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_UniqueCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/FixedWidthInteger+roundUpToPowerOfTwo.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeMutableBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Descriptions.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/RandomAccessCollection+Offsets.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+first\ and\ last\ set\ bit.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+Index.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/_UniqueCollection.swift.o : /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+reversed.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+_Word.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Debugging.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/Integer\ rank.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_SortedCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_UniqueCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/FixedWidthInteger+roundUpToPowerOfTwo.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeMutableBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Descriptions.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/RandomAccessCollection+Offsets.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+first\ and\ last\ set\ bit.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+Index.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/Debugging.swift.o : /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+reversed.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+_Word.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Debugging.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/Integer\ rank.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_SortedCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_UniqueCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/FixedWidthInteger+roundUpToPowerOfTwo.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeMutableBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Descriptions.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/RandomAccessCollection+Offsets.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+first\ and\ last\ set\ bit.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+Index.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/Descriptions.swift.o : /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+reversed.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+_Word.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Debugging.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/Integer\ rank.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_SortedCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_UniqueCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/FixedWidthInteger+roundUpToPowerOfTwo.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeMutableBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Descriptions.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/RandomAccessCollection+Offsets.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+first\ and\ last\ set\ bit.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+Index.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/RandomAccessCollection+Offsets.swift.o : /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+reversed.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+_Word.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Debugging.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/Integer\ rank.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_SortedCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_UniqueCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/FixedWidthInteger+roundUpToPowerOfTwo.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeMutableBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Descriptions.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/RandomAccessCollection+Offsets.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+first\ and\ last\ set\ bit.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+Index.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/UnsafeBufferPointer+Extras.swift.o : /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+reversed.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+_Word.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Debugging.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/Integer\ rank.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_SortedCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_UniqueCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/FixedWidthInteger+roundUpToPowerOfTwo.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeMutableBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Descriptions.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/RandomAccessCollection+Offsets.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+first\ and\ last\ set\ bit.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+Index.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/UnsafeMutableBufferPointer+Extras.swift.o : /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+reversed.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+_Word.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Debugging.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/Integer\ rank.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_SortedCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_UniqueCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/FixedWidthInteger+roundUpToPowerOfTwo.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeMutableBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Descriptions.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/RandomAccessCollection+Offsets.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+first\ and\ last\ set\ bit.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+Index.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/release/Modules/InternalCollectionsUtilities.swiftmodule : /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+reversed.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+_Word.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Debugging.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/Integer\ rank.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_SortedCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_UniqueCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/FixedWidthInteger+roundUpToPowerOfTwo.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeMutableBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Descriptions.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/RandomAccessCollection+Offsets.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+first\ and\ last\ set\ bit.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+Index.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/release/Modules/InternalCollectionsUtilities.swiftdoc : /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+reversed.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+_Word.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Debugging.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/Integer\ rank.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_SortedCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_UniqueCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/FixedWidthInteger+roundUpToPowerOfTwo.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeMutableBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Descriptions.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/RandomAccessCollection+Offsets.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+first\ and\ last\ set\ bit.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+Index.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/release/InternalCollectionsUtilities.build/InternalCollectionsUtilities-Swift.h : /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+reversed.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+_Word.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Debugging.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/Integer\ rank.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_SortedCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_UniqueCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/FixedWidthInteger+roundUpToPowerOfTwo.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeMutableBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Descriptions.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/RandomAccessCollection+Offsets.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+first\ and\ last\ set\ bit.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+Index.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
/Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/arm64-apple-macosx/release/Modules/InternalCollectionsUtilities.swiftsourceinfo : /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+reversed.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+_Word.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Debugging.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/Integer\ rank.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_SortedCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_UniqueCollection.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/FixedWidthInteger+roundUpToPowerOfTwo.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeMutableBufferPointer+Extras.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Descriptions.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/RandomAccessCollection+Offsets.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+first\ and\ last\ set\ bit.swift /Users/<USER>/Documents/Dev\ Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+Index.swift /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/Swift.swiftmodule/arm64e-apple-macos.swiftmodule /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/prebuilt-modules/15.5/_Concurrency.swiftmodule/arm64e-apple-macos.swiftmodule
