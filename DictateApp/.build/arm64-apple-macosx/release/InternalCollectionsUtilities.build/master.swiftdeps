version: "Apple Swift version 6.1.2 (swiftlang-6.1.2.1.2 clang-1700.0.13.5)"
options: "c32cea538d1e582084b649783d0b0c9940dda7c49fe82480127310e2af4fb0ad"
build_start_time: [1750241047, 733708000]
build_end_time: [1750241048, 291522000]
inputs:
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/FixedWidthInteger+roundUpToPowerOfTwo.swift"
  : [1750229487, 686071459]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/Integer rank.swift"
  : [1750229487, 686132251]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+first and last set bit.swift"
  : [1750229487, 686195502]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/IntegerTricks/autogenerated/UInt+reversed.swift"
  : [1750229487, 686262003]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+Index.swift"
  : [1750229487, 686659592]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet+_Word.swift"
  : [1750229487, 686712426]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/UnsafeBitSet/autogenerated/_UnsafeBitSet.swift"
  : [1750229487, 686770260]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_SortedCollection.swift"
  : [1750229487, 686999888]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/_UniqueCollection.swift"
  : [1750229487, 687054306]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Debugging.swift"
  : [1750229487, 687160016]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/Descriptions.swift"
  : [1750229487, 687220558]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/RandomAccessCollection+Offsets.swift"
  : [1750229487, 687286017]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeBufferPointer+Extras.swift"
  : [1750229487, 687343810]
  ? "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-collections/Sources/InternalCollectionsUtilities/autogenerated/UnsafeMutableBufferPointer+Extras.swift"
  : [1750229487, 687411352]
