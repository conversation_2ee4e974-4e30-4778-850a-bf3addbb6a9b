{"commandLine": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-L", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/pm/PluginAPI", "-lPackagePlugin", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/pm/PluginAPI", "-target", "arm64-apple-macosx14.0", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-swift-version", "5", "-package-description-version", "5.8.0", "-I", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/pm/PluginAPI", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-parse-as-library", "-Xfrontend", "-serialize-diagnostics-path", "-Xfrontend", "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/plugins/cache/GenerateManual.dia", "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Plugins/GenerateManual/GenerateManualPlugin.swift", "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Plugins/GenerateManual/GenerateManualPluginError.swift", "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/checkouts/swift-argument-parser/Plugins/GenerateManual/PackagePlugin+Helpers.swift", "-o", "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp/.build/plugins/cache/GenerateManual"], "environment": {"CLAUDE_CODE_SSE_PORT": "45132", "COLORTERM": "truecolor", "COMMAND_MODE": "unix2003", "CPATH": "/usr/local/include", "DOCKER_HOST": "unix:///Users/<USER>/.orbstack/run/docker.sock", "ENABLE_IDE_INTEGRATION": "true", "GIT_ASKPASS": "/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass.sh", "GIT_PAGER": "cat", "HOME": "/Users/<USER>", "HOMEBREW_CELLAR": "/opt/homebrew/Cellar", "HOMEBREW_PREFIX": "/opt/homebrew", "HOMEBREW_REPOSITORY": "/opt/homebrew", "INFOPATH": "/opt/homebrew/share/info:", "LANG": "en_US.UTF-8", "LESS": "-FX", "LIBRARY_PATH": "/usr/local/lib", "LOGNAME": "<PERSON><PERSON><PERSON>", "MANPATH": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/share/man:/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/usr/share/man:/Applications/Xcode.app/Contents/Developer/usr/share/man:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/share/man:", "MallocNanoZone": "0", "OLDPWD": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate", "ORIGINAL_XDG_CURRENT_DESKTOP": "undefined", "PAGER": "cat", "PATH": "/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion.app/Contents/Public:/Users/<USER>/.pyenv/shims:/Users/<USER>/.pyenv/bin:/Users/<USER>/.codeium/windsurf/bin:/Users/<USER>/.orbstack/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/Library/Application Support/Code/User/globalStorage/github.copilot-chat/debugCommand", "PWD": "/Users/<USER>/Documents/Dev Projects/voice_agent001/Dictate/DictateApp", "PYENV_ROOT": "/Users/<USER>/.pyenv", "PYENV_SHELL": "zsh", "SDKROOT": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "SHELL": "/bin/zsh", "SHLVL": "1", "TMPDIR": "/var/folders/1j/ybcdlb8n6891h5yp0n1hhqtw0000gn/T/", "USER": "<PERSON><PERSON><PERSON>", "VSCODE_GIT_ASKPASS_EXTRA_ARGS": "", "VSCODE_GIT_ASKPASS_MAIN": "/Applications/Visual Studio Code.app/Contents/Resources/app/extensions/git/dist/askpass-main.js", "VSCODE_GIT_ASKPASS_NODE": "/Applications/Visual Studio Code.app/Contents/Frameworks/Code Helper (Plugin).app/Contents/MacOS/Code Helper (Plugin)", "VSCODE_GIT_IPC_HANDLE": "/var/folders/1j/ybcdlb8n6891h5yp0n1hhqtw0000gn/T/vscode-git-f943715f33.sock", "XPC_FLAGS": "0x0", "XPC_SERVICE_NAME": "0", "_": "/usr/bin/swift", "__CFBundleIdentifier": "com.microsoft.VSCode", "__CF_USER_TEXT_ENCODING": "0x1F5:0:15"}, "inputHash": "a9e9c91bf2c2e98ae9398a63e98eeff507375ab7cb18290a92d3ac9eae5ea54d", "output": "", "result": {"exit": {"code": 0}}}