#!/usr/bin/env swift

import Foundation

// Test the settings loading mechanism
print("🧪 Testing Settings Loading")

// Simulate AppSettings
struct TestAppSettings: Codable {
    var openAIAPIKey: String = ""
    var openAIModel: String = "gpt-4o"
    
    static let `default` = TestAppSettings()
}

// Test 1: Check what's currently in UserDefaults
print("\n1. Checking current UserDefaults...")

// Check AppSettings key
if let data = UserDefaults.standard.data(forKey: "AppSettings") {
    print("✅ Found AppSettings data in UserDefaults")
    if let savedSettings = try? JSONDecoder().decode(TestAppSettings.self, from: data) {
        print("✅ Successfully decoded AppSettings")
        print("   API Key: \(savedSettings.openAIAPIKey.isEmpty ? "EMPTY" : "SET (\(savedSettings.openAIAPIKey.prefix(10))...)")")
    } else {
        print("❌ Failed to decode AppSettings")
    }
} else {
    print("❌ No AppSettings data found in UserDefaults")
}

// Check legacy key
if let legacyKey = UserDefaults.standard.string(forKey: "OpenAI_API_Key") {
    print("✅ Found legacy API key: \(legacyKey.isEmpty ? "EMPTY" : "SET (\(legacyKey.prefix(10))...)")")
} else {
    print("❌ No legacy API key found")
}

// Check environment variable
if let envKey = ProcessInfo.processInfo.environment["OPENAI_API_KEY"] {
    print("✅ Found environment API key: \(envKey.isEmpty ? "EMPTY" : "SET (\(envKey.prefix(10))...)")")
} else {
    print("❌ No environment API key found")
}

// Test 2: Simulate the loadOpenAIKey function
print("\n2. Simulating loadOpenAIKey() function...")

func testLoadOpenAIKey() -> String {
    print("🔑 Loading OpenAI API key...")
    
    // First check environment variable
    if let envKey = ProcessInfo.processInfo.environment["OPENAI_API_KEY"], !envKey.isEmpty {
        print("🔑 Found API key in environment variable")
        return envKey
    }
    
    // Then check AppSettings (new method)
    if let data = UserDefaults.standard.data(forKey: "AppSettings"),
       let savedSettings = try? JSONDecoder().decode(TestAppSettings.self, from: data),
       !savedSettings.openAIAPIKey.isEmpty {
        print("🔑 Found API key in AppSettings: \(savedSettings.openAIAPIKey.prefix(10))...")
        return savedSettings.openAIAPIKey
    }
    
    // Then check legacy UserDefaults key
    if let savedKey = UserDefaults.standard.string(forKey: "OpenAI_API_Key"), !savedKey.isEmpty {
        print("🔑 Found API key in legacy UserDefaults")
        return savedKey
    }
    
    // If no key found, show a warning
    print("⚠️ No OpenAI API key found. Set OPENAI_API_KEY environment variable or configure in settings.")
    return ""
}

let loadedKey = testLoadOpenAIKey()
print("Result: \(loadedKey.isEmpty ? "NO KEY LOADED" : "KEY LOADED (\(loadedKey.prefix(10))...)")")

print("\n✅ Settings loading test complete")
