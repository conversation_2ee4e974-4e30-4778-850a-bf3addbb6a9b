#!/usr/bin/env swift

import Foundation

print("🧪 UNIT TESTING EXACT FLOW WHERE STRING GETS LOST")

// Mock the exact SystemStatus enum
enum SystemStatus {
    case idle
    case listening(mode: RecordingMode)
    case processing(step: String)
    case error(message: String)
    
    var isRecording: Bool {
        if case .listening = self { return true }
        return false
    }
}

enum RecordingMode {
    case voiceActivity
    case toggle
    case contextual
    case edit
    case contextualEdit
}

// Mock AppState exactly like the real one
class MockAppState {
    var systemStatus: SystemStatus = .idle
    
    func setListening(mode: RecordingMode) {
        systemStatus = .listening(mode: mode)
        print("📱 AppState.setListening called - status now: \(systemStatus)")
    }
    
    func setIdle() {
        systemStatus = .idle
        print("📱 AppState.setIdle called - status now: \(systemStatus)")
    }
    
    func setProcessing(step: String) {
        systemStatus = .processing(step: step)
        print("📱 AppState.setProcessing called - status now: \(systemStatus)")
    }
}

// Mock exact MainViewModel logic
class ExactFlowTester {
    private let appState = MockAppState()
    
    func testCompleteFlow() {
        print("1. Simulating F8 (toggle) recording flow...")
        
        // Step 1: Start recording (F8 pressed)
        print("\n--- STEP 1: Start Recording ---")
        simulateStartRecording(mode: .toggle)
        
        // Step 2: Stop recording (F8 pressed again)  
        print("\n--- STEP 2: Stop Recording ---")
        simulateStopRecording()
        
        // Step 3: Process audio file (happens after recording stops)
        print("\n--- STEP 3: Process Audio File ---")
        let mockTranscription = "Hello world test"
        simulateProcessAudioFile(transcription: mockTranscription)
    }
    
    private func simulateStartRecording(mode: RecordingMode) {
        print("🎯 startRecording called with mode: \(mode)")
        appState.setListening(mode: mode)
        print("✅ Recording started")
    }
    
    private func simulateStopRecording() {
        print("🎯 stopRecording called")
        appState.setIdle()  // THIS IS THE KEY LINE!
        print("✅ Recording stopped")
    }
    
    private func simulateProcessAudioFile(transcription: String) {
        print("🎯 processAudioFile called")
        print("📝 Transcription from whisper: '\(transcription)'")
        
        // Check if empty
        if transcription.isEmpty {
            print("❌ Transcription is empty - would return early")
            return
        }
        
        // Process the transcription
        appState.setProcessing(step: "Processing...")
        let finalText = simulateProcessTranscription(transcription)
        
        print("📝 Final text after processing: '\(finalText)'")
        
        // This is where typeText would be called
        if finalText.isEmpty {
            print("❌ finalText is EMPTY - no typing will happen!")
        } else {
            print("✅ Would call keyboardRepository.typeText('\(finalText)')")
        }
        
        // Add to history (this always happens with original transcription)
        print("📚 Adding to history: '\(transcription)'")
        
        appState.setIdle()
    }
    
    private func simulateProcessTranscription(_ text: String) -> String {
        print("🔄 processTranscription called with: '\(text)'")
        
        // Get current recording mode (THIS IS THE CRITICAL PART)
        let currentMode = getCurrentRecordingMode()
        print("🎯 getCurrentRecordingMode returned: \(currentMode?.description ?? "nil")")
        
        guard let mode = currentMode else {
            print("   → Mode is nil, going to default case")
            let result = processVoiceCommands(text)
            print("   → processVoiceCommands returned: '\(result)'")
            return result
        }
        
        switch mode {
        case .contextual:
            print("   → Contextual mode")
            return "Enhanced: " + text
        case .edit, .contextualEdit:
            print("   → Edit mode - returning empty string")
            return ""
        default:
            print("   → Default case")
            let result = processVoiceCommands(text)
            print("   → processVoiceCommands returned: '\(result)'")
            return result
        }
    }
    
    private func getCurrentRecordingMode() -> RecordingMode? {
        print("🔍 getCurrentRecordingMode called")
        print("🔍 Current app status: \(appState.systemStatus)")
        
        if case .listening(let mode) = appState.systemStatus {
            print("🔍 Returning mode: \(mode)")
            return mode
        }
        print("🔍 Status is not .listening, returning nil")
        return nil
    }
    
    private func processVoiceCommands(_ text: String) -> String {
        print("🎤 processVoiceCommands called with: '\(text)'")
        // Just return as-is for testing
        return text
    }
}

extension RecordingMode {
    var description: String {
        switch self {
        case .voiceActivity: return "voiceActivity"
        case .toggle: return "toggle"
        case .contextual: return "contextual"
        case .edit: return "edit"
        case .contextualEdit: return "contextualEdit"
        }
    }
}

let tester = ExactFlowTester()
tester.testCompleteFlow()

print("\n🎯 THE BUG IS:")
print("By the time processTranscription runs, appState is .idle")
print("So getCurrentRecordingMode returns nil")
print("Should go to default case and return the text")
print("BUT if final text is still empty, the bug is in processVoiceCommands!")